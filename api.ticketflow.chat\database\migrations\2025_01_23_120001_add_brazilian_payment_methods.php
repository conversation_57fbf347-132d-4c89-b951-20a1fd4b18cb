<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class AddBrazilianPaymentMethods extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        // Add new Brazilian delivery payment methods
        DB::table('payments')->insert([
            [
                'tag' => 'cash_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'card_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'pix_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'debit_delivery',
                'input' => 1,
                'sandbox' => 0,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        DB::table('payments')->whereIn('tag', [
            'cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery'
        ])->delete();
    }
}
