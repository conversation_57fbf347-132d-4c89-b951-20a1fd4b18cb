<?php

namespace App\Http\Controllers\API\v1\Rest;

use App\Helpers\ResponseError;
use App\Http\Controllers\Controller;
use App\Http\Resources\DeliveryPaymentMethodResource;
use App\Models\Shop;
use App\Models\ShopDeliveryPaymentSetting;
use Illuminate\Http\JsonResponse;

class DeliveryPaymentMethodController extends RestBaseController
{
    /**
     * Get delivery payment methods for a shop
     *
     * @param int $shopId
     * @return JsonResponse
     */
    public function index(int $shopId): JsonResponse
    {
        try {
            $shop = Shop::find($shopId);
            
            if (!$shop) {
                return $this->onErrorResponse([
                    'code' => ResponseError::ERROR_404,
                    'message' => 'Shop not found'
                ]);
            }

            // Get or create delivery payment settings for the shop
            $settings = ShopDeliveryPaymentSetting::firstOrCreate(
                ['shop_id' => $shopId],
                [
                    'accept_cash_delivery' => true,
                    'accept_card_delivery' => true,
                    'accept_pix_delivery' => true,
                    'accept_debit_delivery' => true,
                    'max_change_amount' => 200.00,
                    'delivery_payment_instructions' => null,
                ]
            );

            $paymentMethods = [];
            $methodDetails = [
                'cash_delivery' => [
                    'tag' => 'cash_delivery',
                    'name' => 'Dinheiro na Entrega',
                    'icon' => '💵',
                    'description' => 'Pagamento em dinheiro no momento da entrega',
                    'supports_change' => true,
                ],
                'card_delivery' => [
                    'tag' => 'card_delivery',
                    'name' => 'Cartão na Entrega',
                    'icon' => '💳',
                    'description' => 'Pagamento com cartão no momento da entrega',
                    'supports_change' => false,
                ],
                'pix_delivery' => [
                    'tag' => 'pix_delivery',
                    'name' => 'PIX na Entrega',
                    'icon' => '📱',
                    'description' => 'Pagamento via PIX no momento da entrega',
                    'supports_change' => false,
                ],
                'debit_delivery' => [
                    'tag' => 'debit_delivery',
                    'name' => 'Débito na Entrega',
                    'icon' => '💳',
                    'description' => 'Pagamento com débito no momento da entrega',
                    'supports_change' => false,
                ],
            ];

            // Add enabled payment methods
            if ($settings->accept_cash_delivery) {
                $paymentMethods[] = $methodDetails['cash_delivery'];
            }
            if ($settings->accept_card_delivery) {
                $paymentMethods[] = $methodDetails['card_delivery'];
            }
            if ($settings->accept_pix_delivery) {
                $paymentMethods[] = $methodDetails['pix_delivery'];
            }
            if ($settings->accept_debit_delivery) {
                $paymentMethods[] = $methodDetails['debit_delivery'];
            }

            return $this->successResponse('success', [
                'shop_id' => $shopId,
                'delivery_payment_methods' => $paymentMethods,
                'max_change_amount' => $settings->max_change_amount,
                'instructions' => $settings->delivery_payment_instructions,
            ]);

        } catch (\Exception $e) {
            return $this->onErrorResponse([
                'code' => ResponseError::ERROR_400,
                'message' => $e->getMessage()
            ]);
        }
    }
}
