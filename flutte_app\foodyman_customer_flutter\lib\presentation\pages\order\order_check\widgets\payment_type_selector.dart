import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodyman/infrastructure/services/app_helpers.dart';
import 'package:foodyman/infrastructure/services/tr_keys.dart';
import 'package:foodyman/presentation/theme/theme.dart';
import 'delivery_payment_methods.dart';
import 'payment_method.dart';

class PaymentTypeSelector extends StatelessWidget {
  final int? shopId;
  final Function(Map<String, dynamic>)? onPaymentSelected;

  const PaymentTypeSelector({
    Key? key,
    this.shopId,
    this.onPaymentSelected,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Pay Now Option
        PaymentTypeCard(
          title: AppHelpers.getTranslation(TrKeys.payNow),
          subtitle: AppHelpers.getTranslation(TrKeys.payNowDescription),
          icon: Icons.payment,
          onTap: () => _showOnlinePaymentMethods(context),
        ),

        16.verticalSpace,

        // Pay on Delivery Option
        PaymentTypeCard(
          title: AppHelpers.getTranslation(TrKeys.payOnDelivery),
          subtitle: AppHelpers.getTranslation(TrKeys.payOnDeliveryDescription),
          icon: Icons.local_shipping,
          onTap: () => _showDeliveryPaymentMethods(context),
        ),
      ],
    );
  }

  void _showOnlinePaymentMethods(BuildContext context) {
    AppHelpers.showCustomModalBottomSheet(
      context: context,
      modal: const PaymentMethods(),
      isDarkMode: false,
      isDrag: true,
      radius: 12,
    );
  }

  void _showDeliveryPaymentMethods(BuildContext context) {
    AppHelpers.showCustomModalBottomSheet(
      context: context,
      modal: DeliveryPaymentMethods(
        shopId: shopId,
        onPaymentSelected: onPaymentSelected,
      ),
      isDarkMode: false,
      isDrag: true,
      radius: 12,
    );
  }
}

class PaymentTypeCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final VoidCallback onTap;

  const PaymentTypeCard({
    Key? key,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: AppStyle.white,
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: AppStyle.borderColor),
        ),
        child: Row(
          children: [
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: AppStyle.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Icon(
                icon,
                color: AppStyle.primary,
                size: 24.r,
              ),
            ),

            16.horizontalSpace,

            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppStyle.interSemi(
                      size: 16,
                      color: AppStyle.black,
                    ),
                  ),
                  4.verticalSpace,
                  Text(
                    subtitle,
                    style: AppStyle.interRegular(
                      size: 14,
                      color: AppStyle.textGrey,
                    ),
                  ),
                ],
              ),
            ),

            Icon(
              Icons.arrow_forward_ios,
              color: AppStyle.textGrey,
              size: 16.r,
            ),
          ],
        ),
      ),
    );
  }
}
