<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ShopDeliveryPaymentSetting extends Model
{
    protected $fillable = [
        'shop_id',
        'accept_cash_delivery',
        'accept_card_delivery',
        'accept_pix_delivery',
        'accept_debit_delivery',
        'max_change_amount',
        'delivery_payment_instructions',
    ];

    protected $casts = [
        'accept_cash_delivery' => 'boolean',
        'accept_card_delivery' => 'boolean',
        'accept_pix_delivery' => 'boolean',
        'accept_debit_delivery' => 'boolean',
        'max_change_amount' => 'decimal:2',
    ];

    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    public function getAcceptedPaymentMethods(): array
    {
        $methods = [];

        if ($this->accept_cash_delivery) $methods[] = 'cash_delivery';
        if ($this->accept_card_delivery) $methods[] = 'card_delivery';
        if ($this->accept_pix_delivery) $methods[] = 'pix_delivery';
        if ($this->accept_debit_delivery) $methods[] = 'debit_delivery';

        return $methods;
    }
}
