<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateShopDeliveryPaymentSettingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::create('shop_delivery_payment_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shop_id')->constrained()->cascadeOnDelete();
            $table->boolean('accept_cash_delivery')->default(true);
            $table->boolean('accept_card_delivery')->default(true);
            $table->boolean('accept_pix_delivery')->default(true);
            $table->boolean('accept_debit_delivery')->default(true);
            $table->decimal('max_change_amount', 10, 2)->default(100.00);
            $table->text('delivery_payment_instructions')->nullable();
            $table->timestamps();

            $table->unique('shop_id');
            $table->index('shop_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::dropIfExists('shop_delivery_payment_settings');
    }
}
