<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BrazilianPaymentMethodsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $paymentMethods = [
            [
                'tag' => 'cash_delivery',
                'input' => 1,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'card_delivery',
                'input' => 1,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'pix_delivery',
                'input' => 1,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'tag' => 'debit_delivery',
                'input' => 1,
                'active' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        foreach ($paymentMethods as $method) {
            // Check if payment method already exists
            $exists = DB::table('payments')->where('tag', $method['tag'])->exists();
            
            if (!$exists) {
                DB::table('payments')->insert($method);
                echo "Inserted payment method: {$method['tag']}\n";
            } else {
                echo "Payment method already exists: {$method['tag']}\n";
            }
        }

        echo "Brazilian payment methods seeder completed successfully!\n";
    }
}
