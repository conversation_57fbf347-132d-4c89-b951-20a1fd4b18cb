import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodyman/infrastructure/services/app_connectivity.dart';
import 'package:foodyman/infrastructure/services/app_helpers.dart';
import 'package:foodyman/domain/interface/shops.dart';
import 'delivery_payment_state.dart';

class DeliveryPaymentNotifier extends StateNotifier<DeliveryPaymentState> {
  final ShopsRepositoryFacade _shopsRepository;

  DeliveryPaymentNotifier(this._shopsRepository) : super(const DeliveryPaymentState());

  void selectPaymentMethod(int index) {
    state = state.copyWith(selectedIndex: index);
  }

  void setChangeRequired(bool required) {
    state = state.copyWith(
      changeRequired: required,
      changeAmount: required ? state.changeAmount : 0.0,
    );
  }

  void setChangeAmount(double amount) {
    state = state.copyWith(changeAmount: amount);
  }

  Future<void> fetchDeliveryPaymentMethods(
    BuildContext context,
    int shopId,
  ) async {
    final connected = await AppConnectivity.connectivity();
    if (connected) {
      state = state.copyWith(isLoading: true);
      final response = await _shopsRepository.getDeliveryPaymentMethods(shopId);
      response.when(
        success: (data) {
          state = state.copyWith(
            methods: data?.deliveryPaymentMethods ?? [],
            instructions: data?.instructions ?? '',
            isLoading: false,
            error: '',
          );
        },
        failure: (failure, status) {
          state = state.copyWith(
            isLoading: false,
            error: AppHelpers.getTranslation(status.toString()),
          );
          AppHelpers.showCheckTopSnackBar(
            context,
            AppHelpers.getTranslation(status.toString()),
          );
        },
      );
    } else {
      if (context.mounted) {
        AppHelpers.showNoConnectionSnackBar(context);
      }
    }
  }

  void reset() {
    state = const DeliveryPaymentState();
  }
}
