<?php

namespace App\Http\Requests\Order;

use App\Http\Requests\BaseRequest;
use App\Models\Order;
use Illuminate\Validation\Rule;

class StoreRequest extends BaseRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'user_id'               => [
                'integer',
                Rule::exists('users', 'id')->whereNull('deleted_at')
            ],
            'waiter_id'             => [
                'integer',
                Rule::exists('users', 'id')->whereNull('deleted_at')
            ],
            'payment_id'            => [
                'integer',
                Rule::exists('payments', 'id')->whereNull('deleted_at')
            ],
            'table_id'              => [
				Rule::exists('tables', 'id')->whereNull('deleted_at')
			],
            'booking_id'            => 'integer',
            'user_booking_id'       => 'integer',
            'currency_id'           => 'required|integer|exists:currencies,id',
            'rate'                  => 'numeric',
            'shop_id'               => [
                'required',
                'integer',
                Rule::exists('shops', 'id')->whereNull('deleted_at')
            ],
            'delivery_fee'          => 'nullable|numeric',
            'waiter_fee'            => 'nullable|numeric',
            'delivery_type'         => ['required', Rule::in(Order::DELIVERY_TYPES)],
            'coupon'                => 'nullable|string',
            'location'              => 'array',
            'location.latitude'     => 'numeric',
            'location.longitude'    => 'numeric',
            'address'               => 'array',
            'address_id'            => ['integer', Rule::exists('user_addresses', 'id')],
            'phone'                 => 'string',
            'email'                 => 'string',
            'username'              => 'string',
            'delivery_date'         => 'date|date_format:Y-m-d',
            'delivery_time'         => 'string',
            'note'                  => 'nullable|string|max:191',
            'cart_id'               => 'integer|exists:carts,id',
            'payment_method'        => [
                'nullable',
                'string',
                Rule::in(Order::PAYMENT_METHODS)
            ],
            'change_required'       => 'boolean',
            'change_amount'         => [
                'nullable',
                'numeric',
                'min:0',
                'required_if:change_required,true'
            ],
            'payment_notes'         => 'nullable|string|max:500',
            'notes'                 => 'array',
            'notes.*'               => 'string|max:255',
            'images'                => 'array',
            'images.*'              => 'string',
			'bonus'                 => 'boolean',
			'tip_type'              => 'in:fix,percent',
			'tips'                  => 'numeric|min:0',
			'delivery_point_id'     => [
				request('delivery_type') === Order::POINT ? 'required' : 'nullable',
				'integer',
				Rule::exists('delivery_points', 'id')
			],
			'products'              => 'nullable|array',
            'products.*.stock_id'   =>  [
                'integer',
                Rule::exists('stocks', 'id')
					->whereNull('deleted_at')
            ],
            'products.*.quantity'   => 'numeric',
            'products.*.note'       => 'nullable|string|max:255',

			'products.*.addons'     => 'array',
			'products.*.addons.*.stock_id'  => [
				'integer',
				Rule::exists('stocks', 'id')
					->where('addon', 1)
					->whereNull('deleted_at')
			],
			'products.*.addons.*.quantity'  => ['integer'],
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'change_amount.required_if' => 'Change amount is required when change is needed.',
            'payment_method.in' => 'Invalid payment method selected.',
        ];
    }
}
