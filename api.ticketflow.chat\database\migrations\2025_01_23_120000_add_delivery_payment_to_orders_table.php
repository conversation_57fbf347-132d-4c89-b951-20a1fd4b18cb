<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeliveryPaymentToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->enum('payment_method', [
                'cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery', 'online'
            ])->nullable()->after('delivery_type');
            
            $table->boolean('change_required')->default(false)->after('payment_method');
            $table->decimal('change_amount', 10, 2)->nullable()->after('change_required');
            $table->text('payment_notes')->nullable()->after('change_amount');
            
            $table->index('payment_method');
            $table->index('change_required');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex(['payment_method']);
            $table->dropIndex(['change_required']);
            $table->dropColumn(['payment_method', 'change_required', 'change_amount', 'payment_notes']);
        });
    }
}
