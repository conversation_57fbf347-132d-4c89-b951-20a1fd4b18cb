<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class CreateDatabaseDump extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:dump 
                            {--output= : Output file path (default: storage/app/dumps/)}
                            {--compress : Compress the dump file with gzip}
                            {--no-data : Export structure only, no data}
                            {--tables= : Comma-separated list of tables to dump}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a complete MySQL database dump with all structures, data, and constraints';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🗄️  Starting complete database dump...');
        $this->newLine();

        try {
            // Get database configuration
            $config = $this->getDatabaseConfig();
            
            // Validate connection
            $this->validateConnection();
            
            // Create output directory
            $outputPath = $this->prepareOutputPath();
            
            // Create the dump
            $dumpFile = $this->createDump($config, $outputPath);
            
            // Add custom header
            $this->addCustomHeader($dumpFile, $config);
            
            // Compress if requested
            if ($this->option('compress')) {
                $dumpFile = $this->compressFile($dumpFile);
            }
            
            // Show statistics
            $this->showStatistics($dumpFile);
            
            $this->info('✅ Database dump completed successfully!');
            $this->info("📁 File: {$dumpFile}");
            
        } catch (\Exception $e) {
            $this->error('❌ Database dump failed: ' . $e->getMessage());
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    /**
     * Get database configuration
     */
    private function getDatabaseConfig(): array
    {
        $connection = config('database.default');
        $config = config("database.connections.{$connection}");
        
        if (!$config || $config['driver'] !== 'mysql') {
            throw new \Exception('Only MySQL databases are supported');
        }
        
        return $config;
    }

    /**
     * Validate database connection
     */
    private function validateConnection(): void
    {
        $this->info('🔍 Testing database connection...');
        
        try {
            DB::connection()->getPdo();
            $this->info('✅ Database connection successful');
        } catch (\Exception $e) {
            throw new \Exception('Database connection failed: ' . $e->getMessage());
        }
    }

    /**
     * Prepare output path
     */
    private function prepareOutputPath(): string
    {
        $outputDir = $this->option('output') ?: storage_path('app/dumps');
        
        if (!is_dir($outputDir)) {
            mkdir($outputDir, 0755, true);
        }
        
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "database_dump_{$timestamp}.sql";
        
        return $outputDir . DIRECTORY_SEPARATOR . $filename;
    }

    /**
     * Create the database dump
     */
    private function createDump(array $config, string $outputPath): string
    {
        $this->info('📦 Creating database dump...');
        
        $command = $this->buildMysqldumpCommand($config, $outputPath);
        
        $this->line("Executing: " . str_replace($config['password'], '***', $command));
        
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            throw new \Exception('mysqldump failed: ' . implode("\n", $output));
        }
        
        if (!file_exists($outputPath) || filesize($outputPath) === 0) {
            throw new \Exception('Dump file was not created or is empty');
        }
        
        return $outputPath;
    }

    /**
     * Build mysqldump command
     */
    private function buildMysqldumpCommand(array $config, string $outputPath): string
    {
        $command = 'mysqldump';
        
        // Connection parameters
        $command .= " --host=" . escapeshellarg($config['host']);
        $command .= " --port=" . escapeshellarg($config['port']);
        $command .= " --user=" . escapeshellarg($config['username']);
        $command .= " --password=" . escapeshellarg($config['password']);
        
        // Dump options
        if (!$this->option('no-data')) {
            $command .= ' --single-transaction';      // Consistent backup for InnoDB
            $command .= ' --complete-insert';         // Complete INSERT statements
            $command .= ' --extended-insert';         // Multiple-row INSERT syntax
        } else {
            $command .= ' --no-data';                 // Structure only
        }
        
        $command .= ' --routines';                    // Stored procedures and functions
        $command .= ' --triggers';                    // Triggers
        $command .= ' --events';                      // Events
        $command .= ' --add-drop-table';              // DROP TABLE before CREATE
        $command .= ' --add-drop-trigger';            // DROP TRIGGER before CREATE
        $command .= ' --create-options';              // MySQL-specific table options
        $command .= ' --disable-keys';                // Disable keys during import
        $command .= ' --lock-tables=false';           // Don't lock tables
        $command .= ' --set-gtid-purged=OFF';         // Disable GTID
        $command .= ' --default-character-set=utf8mb4'; // Character set
        $command .= ' --hex-blob';                    // Binary data in hex
        $command .= ' --comments';                    // Include comments
        $command .= ' --dump-date';                   // Include dump date
        
        // Specific tables if requested
        if ($tables = $this->option('tables')) {
            $tableList = explode(',', $tables);
            $command .= ' ' . implode(' ', array_map('trim', $tableList));
        }
        
        // Database name
        $command .= " " . escapeshellarg($config['database']);
        
        // Output to file
        $command .= " > " . escapeshellarg($outputPath);
        
        return $command;
    }

    /**
     * Add custom header to dump file
     */
    private function addCustomHeader(string $dumpFile, array $config): void
    {
        $this->info('📝 Adding custom header...');
        
        $currentContent = file_get_contents($dumpFile);
        
        $mysqlVersion = $this->getMySQLVersion();
        $laravelVersion = app()->version();
        
        $header = "-- =====================================================\n";
        $header .= "-- COMPLETE DATABASE DUMP\n";
        $header .= "-- Database: {$config['database']}\n";
        $header .= "-- Generated: " . now()->format('Y-m-d H:i:s') . "\n";
        $header .= "-- MySQL Version: {$mysqlVersion}\n";
        $header .= "-- Laravel Version: {$laravelVersion}\n";
        $header .= "-- Host: {$config['host']}:{$config['port']}\n";
        $header .= "-- =====================================================\n";
        $header .= "-- \n";
        $header .= "-- This dump includes:\n";
        $header .= "-- ✓ All table structures (CREATE TABLE statements)\n";
        
        if (!$this->option('no-data')) {
            $header .= "-- ✓ All data (INSERT statements)\n";
        }
        
        $header .= "-- ✓ All indexes and constraints\n";
        $header .= "-- ✓ All foreign key relationships\n";
        $header .= "-- ✓ All triggers\n";
        $header .= "-- ✓ All stored procedures and functions\n";
        $header .= "-- ✓ All events\n";
        $header .= "-- \n";
        $header .= "-- IMPORT INSTRUCTIONS:\n";
        $header .= "-- 1. Create target database: CREATE DATABASE your_database_name;\n";
        $header .= "-- 2. Use database: USE your_database_name;\n";
        $header .= "-- 3. Import dump: SOURCE /path/to/this/file.sql;\n";
        $header .= "-- OR: mysql -u username -p database_name < /path/to/this/file.sql\n";
        $header .= "-- =====================================================\n\n";
        
        file_put_contents($dumpFile, $header . $currentContent);
    }

    /**
     * Get MySQL version
     */
    private function getMySQLVersion(): string
    {
        try {
            $result = DB::select('SELECT VERSION() as version');
            return $result[0]->version ?? 'Unknown';
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }

    /**
     * Compress the dump file
     */
    private function compressFile(string $dumpFile): string
    {
        $this->info('🗜️  Compressing dump file...');
        
        $compressedFile = $dumpFile . '.gz';
        $command = "gzip -c " . escapeshellarg($dumpFile) . " > " . escapeshellarg($compressedFile);
        
        exec($command, $output, $returnCode);
        
        if ($returnCode === 0 && file_exists($compressedFile)) {
            unlink($dumpFile); // Remove original file
            return $compressedFile;
        }
        
        $this->warn('⚠️  Compression failed, keeping uncompressed file');
        return $dumpFile;
    }

    /**
     * Show dump statistics
     */
    private function showStatistics(string $dumpFile): void
    {
        $this->info('📊 Dump Statistics:');
        
        $size = $this->formatBytes(filesize($dumpFile));
        $this->line("File size: {$size}");
        
        if (str_ends_with($dumpFile, '.gz')) {
            $this->line("Format: Compressed (gzip)");
        } else {
            // Count lines and statements for uncompressed files
            $content = file_get_contents($dumpFile);
            $lines = substr_count($content, "\n");
            $tables = substr_count($content, 'CREATE TABLE');
            $inserts = substr_count($content, 'INSERT INTO');
            
            $this->line("Total lines: " . number_format($lines));
            $this->line("Tables: {$tables}");
            $this->line("Insert statements: " . number_format($inserts));
        }
    }

    /**
     * Format bytes to human readable format
     */
    private function formatBytes(int $size, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
            $size /= 1024;
        }
        
        return round($size, $precision) . ' ' . $units[$i];
    }
}
