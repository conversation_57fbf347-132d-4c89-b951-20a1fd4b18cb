import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:venderfoodyman/presentation/styles/style.dart';
import '../helper/common_image.dart';
import '../buttons/buttons_bouncing_effect.dart';
import 'package:venderfoodyman/infrastructure/models/models.dart';
import 'package:venderfoodyman/infrastructure/services/services.dart';

class OrderItem extends StatelessWidget {
  final OrderData order;
  final bool isHistoryOrder;
  final VoidCallback onTap;

  const OrderItem({
    super.key,
    required this.order,
    required this.onTap,
    this.isHistoryOrder = false,
  });

  @override
  Widget build(BuildContext context) {
    return ButtonsBouncingEffect(
      child: GestureDetector(
        onTap: onTap,
        child: Container(
          height: 134.h,
          width: double.infinity,
          margin: REdgeInsets.only(bottom: 10),
          padding: REdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Style.white,
            borderRadius: BorderRadius.circular(10.r),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        CommonImage(
                          url: order.user?.img,
                          radius: 25,
                          width: 50,
                          height: 50,
                        ),
                        12.horizontalSpace,
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                order.user == null
                                    ? AppHelpers.getTranslation(
                                        TrKeys.deletedUser)
                                    : '${order.user?.firstname ?? AppHelpers.getTranslation(TrKeys.noName)} ${order.user?.lastname ?? ''}',
                                style: Style.interRegular(
                                  size: 14.sp,
                                  color: Style.blackColor,
                                ),
                              ),
                              4.verticalSpace,
                              Text(
                                AppHelpers.getTranslation(
                                    order.deliveryType ?? ""),
                                style: Style.interNormal(
                                  size: 12.sp,
                                  color: Style.blackColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (AppHelpers.getOrderStatus(order.status) ==
                      OrderStatus.newOrder)
                    Container(
                      width: 10.r,
                      height: 10.r,
                      decoration: const BoxDecoration(
                        shape: BoxShape.circle,
                        color: Style.red,
                      ),
                    ),
                  if (isHistoryOrder)
                    _buildPaymentInfo()
                ],
              ),
              14.verticalSpace,
              Divider(color: Style.greyColor, thickness: 1.r, height: 1.r),
              14.verticalSpace,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  RichText(
                    text: TextSpan(
                      text: '№ ${order.id}',
                      style: Style.interNormal(
                        color: Style.blackColor,
                        size: 14.sp,
                        letterSpacing: -0.3,
                      ),
                      children: [
                        TextSpan(
                          text: ' | ',
                          style: Style.interNormal(
                            color: Style.borderColor,
                            size: 14.sp,
                            letterSpacing: -0.3,
                          ),
                        ),
                        TextSpan(
                          text: '${order.deliveryDate ?? ''} ${order.deliveryTime ?? ''}',
                          style: Style.interNormal(
                            color: Style.blackColor,
                            size: 14.sp,
                            letterSpacing: -0.3,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    AppHelpers.numberFormat(
                         order.totalPrice?.isNegative ?? true
                            ? 0
                            : order.totalPrice ?? 0,
                        symbol: order.currency?.symbol),
                    style:
                        Style.interNormal(size: 14.sp, color: Style.blackColor),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentInfo() {
    if (_isDeliveryPayment()) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _getPaymentIcon(),
              4.horizontalSpace,
              Text(
                _getPaymentMethodName(),
                style: Style.interMedium(
                  size: 11.sp,
                  color: Style.blackColor,
                ),
              ),
            ],
          ),
          if (order.paymentMethod == 'cash_delivery' && (order.changeRequired ?? false))
            Container(
              margin: EdgeInsets.only(top: 2.h),
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: Style.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                'Troco: ${AppHelpers.numberFormat(number: order.changeAmount ?? 0, symbol: order.currency?.symbol)}',
                style: Style.interMedium(
                  size: 9.sp,
                  color: Style.red,
                ),
              ),
            ),
        ],
      );
    } else {
      return Text(
        AppHelpers.getTranslation(order.transaction?.paymentSystem?.tag ?? ''),
        style: Style.interMedium(
          size: 11.sp,
          color: Style.blackColor,
        ),
      );
    }
  }

  bool _isDeliveryPayment() {
    return ['cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery']
        .contains(order.paymentMethod);
  }

  Widget _getPaymentIcon() {
    switch (order.paymentMethod) {
      case 'cash_delivery':
        return Text('💵', style: TextStyle(fontSize: 12.sp));
      case 'card_delivery':
        return Icon(Icons.credit_card, size: 12.sp, color: Style.blackColor);
      case 'pix_delivery':
        return Text('📱', style: TextStyle(fontSize: 12.sp));
      case 'debit_delivery':
        return Icon(Icons.payment, size: 12.sp, color: Style.blackColor);
      default:
        return Icon(Icons.payment, size: 12.sp, color: Style.blackColor);
    }
  }

  String _getPaymentMethodName() {
    switch (order.paymentMethod) {
      case 'cash_delivery':
        return AppHelpers.getTranslation(TrKeys.cashDelivery);
      case 'card_delivery':
        return AppHelpers.getTranslation(TrKeys.cardDelivery);
      case 'pix_delivery':
        return AppHelpers.getTranslation(TrKeys.pixDelivery);
      case 'debit_delivery':
        return AppHelpers.getTranslation(TrKeys.debitDelivery);
      default:
        return AppHelpers.getTranslation(TrKeys.onlinePayment);
    }
  }
}
