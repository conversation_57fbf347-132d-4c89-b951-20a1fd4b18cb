abstract class TrKeys {
  TrKeys._();
  static const String select = 'select';
  static const String gallery = 'gallery';
  static const String sku = 'sku';
  static const String bonus = 'bonus';
  static const String shopBonus = 'shop.bonus';
  static const String tips = 'tips';
  static const String table = 'table';
  static const String input = 'input';
  static const String user = 'user';
  static const String kitchen = 'kitchen';
  static const String uploadDocuments = 'upload.documents';
  static const String kitchens = 'kitchens';
  static const String available = 'available';
  static const String deliveryFee = "delivery.fee";
  static const String serviceFee = "service.fee";
  static const String discount = "discount";
  static const String coupon = "coupon";
  static const String before = 'before';
  static const String otpCode = 'otp.code';
  static const String after = 'after';
  static const String waiter = 'waiter';
  static const String pickup = 'pickup';
  static const String dineIn = 'dine_in';
  static const String needOrder = 'need.order';
  static const String selectedSection = 'selected.section';
  static const String selectedTable = 'selected.table';
  static const String pleaseSelectASection = 'please_select_a_section';
  static const String pleaseSelectATable = 'please_select_a_table';
  static const String selectTable = 'select.table';
  static const String priceInformation = 'price.information';
  static const String imageGenerateTake = 'image_generate_take';
  static const String typeSomething = 'type_something';
  static const String locationCanNotBeEmpty = 'location.can.not.be.empty';
  static const String logoCanNotBeEmpty = 'logo.can.no.be.empty';
  static const String bgCanNotBeEmpty = 'bg.can.no.be.empty';
  static const String generateImageWithChatGPT = 'generate_image_with_chatGPT';
  static const String profile = 'profile';
  static const String readAll = 'read_all';
  static const String orderImage = 'order.image';
  static const String id = 'id';
  static const String thisImageWasUploadDriver = 'this.image.was.uploaded.by.driver';
  static const String areYouSure = 'are.you.sure';
  static const String deleteAccount = 'delete.account';
  static const String userAlready = 'user.already';
  static const String approved = 'approved';
  static const String deletedUser = 'deleted.user';
  static const String yourShopRejected = 'your.shop.rejected';
  static const String yourOrderStatusChanged =
      'your_order_status_has_been_changed';
  static const String phoneNumberIsNotValid = 'phone_number_is_not_valid';
  static const String open = 'open';
  static const String logout = 'logout';
  static const String thanksForCategory = 'thanks.for.category';
  static const String subShopCategory = 'sub.shop.category';
  static const String swipeToWay = 'swipe_to_way';
  static const String swipeToDelivered = 'swipe_to_delivered';
  static const String swipeToReady = 'swipe_to_ready';
  static const String showProduct = 'show_the_product_to_the_customer';
  static const String addComment = 'add_comment';
  static const String comment = 'comment';
  static const String interval = 'interval';
  static const String categoryName = 'category_name';
  static const String categories = 'categories';
  static const String addNewCategory = 'add_new_category';
  static const String productCategory = 'product_category';
  static const String productTitle = 'product.title';
  static const String recommendedSize = "recommended_size_is";
  static const String productPicture = 'product_picture';
  static const String addProduct = 'add_product';
  static const String total = 'total';
  static const String totalTax = 'total_tax';
  static const String subtotal = 'subtotal';
  static const String deliveryPrice = 'delivery_price';
  static const String payment = 'payment';
  static const String cashPayment = 'cash_payment_when_the_courier_arrives';
  static const String selectedTimeAndDay = 'selected_time_and_day';
  static const String approximateTime = 'spproximate_preparation_time: ';
  static const String takeAway = 'take_away';
  static const String estimatedTime = 'estimated_delivery_time:';
  static const String deliveryService = 'delivery_service';
  static const String deliveryType = 'delivery.type';
  static const String customerInformation = 'customer.information';
  static const String house = 'house';
  static const String floor = 'floor';
  static const String asap = 'asap';
  static const String entrance = 'entrance';
  static const String target = 'target';
  static const String selectedAddress = 'selected_address';
  static const String shippingAddress = 'shipping_address';
  static const String clearAllOrders = 'clear_all_orders';
  static const String next = 'next';
  static const String somethingWentWrongWithTheServer =
      'something_went_wrong_with_the_server';
  static const String ordering = 'ordering';
  static const String toBuy = 'to_buy';
  static const String resendOtp = 'send_new';
  static const String sendOtp = 'we_are_send_OTP_code_to';
  static const String enterOtp = 'enter_OTP_code';
  static const String checkYourNetworkConnection =
      'check_your_network_connection';
  static const String send = "send";
  static const String resetPasswordText =
      "reset_password_text";
  static const String orAccessQuickly = 'or_access_quickly';
  static const String forgotPassword = 'forgot_password';
  static const String keepMeLoggedIn = 'keep_me_logged_in';
  static const String login = 'login';
  static const String noProducts =
      "no_products";
  static const String noNotices = "no_notices";
  static const String fm = "fm";
  static const String restaurant = "restaurant";
  static const String price = "price";
  static const String time = "time";
  static const String moreOrders = "more_details_about_all_orders";
  static const String deliveredOrdersCount = 'delivered.orders.count';
  static const String rejectedOrders = "rejected_orders";
  static const String cancelOrders = "cancel_orders";
  static const String acceptedOrders = 'accepted_orders';
  static const String newOrders = 'new_orders';
  static const String totalOrders = 'total.orders';
  static const String statistics = 'statistics';
  static const String earningsChart = "earnings_chart";
  static const String moreAboutOrders = "more_about_orders";
  static const String fMRevenue = "fm_revenue";
  static const String restaurantRevenue = "restaurant_revenue";
  static const String lastIncome = "last_income";
  static const String orderPrice = 'order_price';
  static const String withdrawMoney = "withdraw_money";
  static const String earningsRestaurant = "earnings_of_the_restaurant";
  static const String income = "income";
  static const String selectDesiredOrderHistory =
      "select_desired_order_history";
  static const String filter = "filter";
  static const String today = "today";
  static const String overall = "overall";
  static const String monthly = "monthly";
  static const String weekly = "weekly";
  static const String customerOrder = "customer_order";
  static const String foodymanBenefit = "foodyman_benefit";
  static const String yourBenefit = "your_benefit";
  static const String thereAre = "there_are";
  static const String orderHistory = "order_history";
  static const String setBusinessDay = 'set_as_a_business_day';
  static const String enterOpeningHours = 'enter_the_restaurant_opening_hours';
  static const String workingHours = 'working_hours';
  static const String locationConfirmation = 'location_confirmation';
  static const String save = 'save';
  static const String address = 'address';
  static const String password = 'password';
  static const String email = 'email';
  static const String phoneNumber = 'phone_number';
  static const String description = 'description';
  static const String restaurantName = 'restaurant_name';
  static const String settings = 'settings';
  static const String myOrderHistory = 'my_order_history';
  static const String restaurantSettings = 'restaurant_settings';
  static const String sections = 'sections';
  static const String seeAll = 'see_all';
  static const String notifications = 'notifications';
  static const String balance = 'balance';
  static const String ingredients = 'ingredients';
  static const String size = 'size';
  static const String inactiveTime = 'choose_inactive_meal_time';
  static const String parameters = 'parameters';
  static const String search = 'search';
  static const String sideDish = 'side_dish';
  static const String amount = 'amount';
  static const String products = 'products';
  static const String delivery = 'delivery';
  static const String orders = 'orders';
  static const String noInternetConnection = 'no_internet_connection';
  static const String resetPassword = 'reset_password';
  static const String confirmation = 'confirm';
  static const String confirmPassword = 'confirm_password';
  static const String smsDidntSend = 'sms_didnt_send';
  static const String loginCredentialsAreNotValid =
      'login_credentials_are_not_valid';
  static const String doYouReallyWantToLogout = 'do_you_really_want_to_logout';
  static const String yes = 'yes';
  static const String emailIsNotValid = 'email_is_not_valid';
  static const String passwordShouldContainMinimum6Characters =
      'password_should_contain_minimum_6_characters';
  static const String language = 'language';
  static const String cancel = 'cancel';
  static const String successfullyCompleted = 'successfully_completed';
  static const String infoMessage = 'info_message';
  static const String youAreASeller = 'you_are_a_seller';
  static const String youAreAnAdmin = 'you_are_an_admin';
  static const String accessDenied = 'access_denied';
  static const String readyOrders = 'ready_orders';
  static const String onAWayOrders = 'on_a_way_orders';
  static const String successfullyUpdated = 'successfully_updated';
  static const String editProduct = 'edit_product';
  static const String cannotBeEmpty = 'cannot_be_empty';
  static const String updateFailed = 'update_failed';
  static const String quantity = 'quantity';
  static const String stocks = 'stocks';
  static const String close = 'close';
  static const String units = 'units';
  static const String extras = 'extras';
  static const String maxQuantity = 'max_quantity';
  static const String minQuantity = 'min_quantity';
  static const String minQuantityError = 'min_quantity_error';
  static const String tax = 'tax';
  static const String deliveryZone = 'delivery_zone';
  static const String theRestaurantIsClosedToday =
      'the_restaurant_is_closed_today';
  static const String confirmLocation = 'confirm_location';
  static const String searchLocation = 'search_location';
  static const String selectDeliveryDate = 'select_delivery_date';
  static const String deliveryTime = 'delivery_time';
  static const String outOfStock = 'out_of_stock';
  static const String pleaseSelectAUser = 'please_select_a_user';
  static const String popular = 'popular';
  static const String swipeToAccept = 'swipe_to_accept';
  static const String order = 'order';
  static const String noOrders = 'no_orders';
  static const String noName = 'no_name';
  static const String noTransaction = 'no_transaction';
  static const String loading = 'loading';
  static const String clearAll = 'clear_all';
  static const String rating = 'rating';
  static const String show = 'show';
  static const String orderPayment = 'order.payment';
  static const String shopAndRestaurants = 'shop_and_restaurants';
  static const String edit = 'edit';
  static const String published = 'published';
  static const String pending = 'pending';
  static const String successfullyCreated = 'successfully_created';
  static const String minQtyCannotBeGreaterThanMaxQty =
      'min_qty_cannot_be_greater_than_max_qty';
  static const String maxQtyShouldBeGreaterThanMinQty =
      'max_qty_should_be_greater_than_min_qty';
  static const String selectedUser = 'selected_user';
  static const String addUser = 'add.user';
  static const String firstname = 'firstname';
  static const String lastname = 'lastname';
  static const String failed = 'failed';
  static const String foods = 'foods';
  static const String addons = 'addons';
  static const String active = 'active';

  /// not added yet
  static const String details = 'details';
  static const String title = 'title';
  static const String addNewExtrasGroup = 'add_new_extras_group';
  static const String addNewExtras = 'add_new_extras';
  static const String areYouSureToDelete = 'are_you_sure_to_delete';
  static const String noData = 'no_data';
  static const String notEnoughMoney = 'no_enough_money';
  static const String demoLoginPassword = 'demo_login_password';
  static const String emailOrPhone = 'email_or_phone';
  static const String surname = 'surname';
  static const String register = 'register';
  static const String referral = 'referral';
  static const String startPrice = 'start_price';
  static const String deliveryTimeType = 'delivery_time_type';
  static const String deliveryTimeFrom = 'delivery_time_from';
  static const String deliveryTimeTo = 'delivery_time_to';
  static const String pricePerKm = 'price_per_km';
  static const String becomeSeller = 'become_seller';
  static const String yourRequest = 'your_request';
  static const String agreeLocation = 'agree_location';
  static const String goToAdminPanel = 'go_to_admin_panel';
  static const String apply = 'apply';
  static const String enterADeliveryAddress = 'enter_a_delivery_address';
  static const String confirmPasswordIsNotTheSame = 'confirm_password_is_not_the_same';
  static const String pleaseTryAgain = 'your_request_rejected_please_try_again';
  static const String note = 'note';

  // Brazilian Payment Methods
  static const String cashDelivery = 'cash_delivery';
  static const String cardDelivery = 'card_delivery';
  static const String pixDelivery = 'pix_delivery';
  static const String debitDelivery = 'debit_delivery';
  static const String onlinePayment = 'online_payment';

}
