<?php

namespace Tests\Unit;

use App\Models\Order;
use App\Models\Shop;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderModelTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_has_brazilian_payment_method_constants()
    {
        $this->assertEquals('cash_delivery', Order::PAYMENT_METHOD_CASH_DELIVERY);
        $this->assertEquals('card_delivery', Order::PAYMENT_METHOD_CARD_DELIVERY);
        $this->assertEquals('pix_delivery', Order::PAYMENT_METHOD_PIX_DELIVERY);
        $this->assertEquals('debit_delivery', Order::PAYMENT_METHOD_DEBIT_DELIVERY);
    }

    /** @test */
    public function it_can_check_if_payment_method_is_delivery_payment()
    {
        $order = new Order();
        
        $order->payment_method = 'cash_delivery';
        $this->assertTrue($order->isDeliveryPayment());
        
        $order->payment_method = 'card_delivery';
        $this->assertTrue($order->isDeliveryPayment());
        
        $order->payment_method = 'pix_delivery';
        $this->assertTrue($order->isDeliveryPayment());
        
        $order->payment_method = 'debit_delivery';
        $this->assertTrue($order->isDeliveryPayment());
        
        $order->payment_method = 'online';
        $this->assertFalse($order->isDeliveryPayment());
        
        $order->payment_method = null;
        $this->assertFalse($order->isDeliveryPayment());
    }

    /** @test */
    public function it_can_check_if_payment_method_supports_change()
    {
        $order = new Order();
        
        $order->payment_method = 'cash_delivery';
        $this->assertTrue($order->paymentMethodSupportsChange());
        
        $order->payment_method = 'card_delivery';
        $this->assertFalse($order->paymentMethodSupportsChange());
        
        $order->payment_method = 'pix_delivery';
        $this->assertFalse($order->paymentMethodSupportsChange());
        
        $order->payment_method = 'debit_delivery';
        $this->assertFalse($order->paymentMethodSupportsChange());
    }

    /** @test */
    public function it_can_get_payment_method_display_name()
    {
        $order = new Order();
        
        $order->payment_method = 'cash_delivery';
        $this->assertEquals('Dinheiro na Entrega', $order->getPaymentMethodDisplayName());
        
        $order->payment_method = 'card_delivery';
        $this->assertEquals('Cartão na Entrega', $order->getPaymentMethodDisplayName());
        
        $order->payment_method = 'pix_delivery';
        $this->assertEquals('PIX na Entrega', $order->getPaymentMethodDisplayName());
        
        $order->payment_method = 'debit_delivery';
        $this->assertEquals('Débito na Entrega', $order->getPaymentMethodDisplayName());
        
        $order->payment_method = 'unknown';
        $this->assertEquals('unknown', $order->getPaymentMethodDisplayName());
    }

    /** @test */
    public function it_can_get_payment_instructions()
    {
        $order = new Order();
        
        $order->payment_method = 'cash_delivery';
        $this->assertEquals('Tenha o dinheiro separado para facilitar o troco.', $order->getPaymentInstructions());
        
        $order->payment_method = 'card_delivery';
        $this->assertEquals('O entregador levará a máquina de cartão.', $order->getPaymentInstructions());
        
        $order->payment_method = 'pix_delivery';
        $this->assertEquals('O entregador levará um terminal PIX para o pagamento.', $order->getPaymentInstructions());
        
        $order->payment_method = 'debit_delivery';
        $this->assertEquals('O entregador levará a máquina de débito.', $order->getPaymentInstructions());
        
        $order->payment_method = 'online';
        $this->assertNull($order->getPaymentInstructions());
    }

    /** @test */
    public function it_can_calculate_change_amount()
    {
        $order = Order::factory()->create([
            'payment_method' => 'cash_delivery',
            'change_required' => true,
            'change_amount' => 100.00,
            'total_price' => 75.50,
        ]);

        $this->assertEquals(24.50, $order->calculateChangeToReturn());
    }

    /** @test */
    public function it_returns_zero_change_when_no_change_required()
    {
        $order = Order::factory()->create([
            'payment_method' => 'cash_delivery',
            'change_required' => false,
            'total_price' => 75.50,
        ]);

        $this->assertEquals(0, $order->calculateChangeToReturn());
    }

    /** @test */
    public function it_can_scope_orders_by_payment_method()
    {
        $shop = Shop::factory()->create();
        $user = User::factory()->create();

        Order::factory()->create([
            'shop_id' => $shop->id,
            'user_id' => $user->id,
            'payment_method' => 'cash_delivery'
        ]);
        
        Order::factory()->create([
            'shop_id' => $shop->id,
            'user_id' => $user->id,
            'payment_method' => 'card_delivery'
        ]);
        
        Order::factory()->create([
            'shop_id' => $shop->id,
            'user_id' => $user->id,
            'payment_method' => 'online'
        ]);

        $cashOrders = Order::byPaymentMethod('cash_delivery')->get();
        $this->assertCount(1, $cashOrders);
        $this->assertEquals('cash_delivery', $cashOrders->first()->payment_method);

        $deliveryOrders = Order::byDeliveryPayment()->get();
        $this->assertCount(2, $deliveryOrders);
    }

    /** @test */
    public function it_can_scope_orders_requiring_change()
    {
        $shop = Shop::factory()->create();
        $user = User::factory()->create();

        Order::factory()->create([
            'shop_id' => $shop->id,
            'user_id' => $user->id,
            'payment_method' => 'cash_delivery',
            'change_required' => true,
            'change_amount' => 100.00
        ]);
        
        Order::factory()->create([
            'shop_id' => $shop->id,
            'user_id' => $user->id,
            'payment_method' => 'cash_delivery',
            'change_required' => false
        ]);

        $ordersRequiringChange = Order::requiresChange()->get();
        $this->assertCount(1, $ordersRequiringChange);
        $this->assertTrue($ordersRequiringChange->first()->change_required);
    }
}
