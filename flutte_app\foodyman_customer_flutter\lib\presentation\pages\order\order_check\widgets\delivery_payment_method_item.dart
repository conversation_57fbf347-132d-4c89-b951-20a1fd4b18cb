import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:foodyman/infrastructure/models/data/delivery_payment_method.dart';
import 'package:foodyman/presentation/theme/theme.dart';

class DeliveryPaymentMethodItem extends StatelessWidget {
  final DeliveryPaymentMethod method;
  final bool isSelected;
  final VoidCallback onTap;

  const DeliveryPaymentMethodItem({
    Key? key,
    required this.method,
    required this.isSelected,
    required this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.only(bottom: 12.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: isSelected ? AppStyle.primary.withOpacity(0.1) : AppStyle.white,
          border: Border.all(
            color: isSelected ? AppStyle.primary : AppStyle.borderColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            // Payment Method Icon
            Container(
              width: 48.w,
              height: 48.h,
              decoration: BoxDecoration(
                color: AppStyle.primary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Center(
                child: Text(
                  method.icon ?? '💳',
                  style: TextStyle(fontSize: 24.sp),
                ),
              ),
            ),

            16.horizontalSpace,

            // Payment Method Info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    method.name ?? '',
                    style: AppStyle.interSemi(
                      size: 16,
                      color: AppStyle.black,
                    ),
                  ),
                  4.verticalSpace,
                  Text(
                    method.description ?? '',
                    style: AppStyle.interRegular(
                      size: 14,
                      color: AppStyle.textGrey,
                    ),
                  ),
                ],
              ),
            ),

            // Selection Indicator
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppStyle.primary,
                size: 24.r,
              ),
          ],
        ),
      ),
    );
  }
}
