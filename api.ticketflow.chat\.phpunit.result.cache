{"version": 1, "defects": {"Tests\\Feature\\DeliveryPaymentMethodsTest::it_can_get_delivery_payment_methods_for_shop": 3, "Tests\\Feature\\DeliveryPaymentMethodsTest::it_returns_default_settings_when_no_settings_exist": 3, "Tests\\Feature\\DeliveryPaymentMethodsTest::it_returns_404_for_non_existent_shop": 3, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_can_create_order_with_cash_delivery_payment": 3, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_can_create_order_with_card_delivery_payment": 3, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_can_create_order_with_pix_delivery_payment": 3, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_validates_change_amount_does_not_exceed_max_limit": 3, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_validates_change_amount_is_greater_than_total_price": 3, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_validates_payment_method_is_accepted_by_shop": 4, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_requires_change_amount_when_change_is_required": 3, "Tests\\Unit\\OrderModelTest::it_can_check_if_payment_method_supports_change": 4, "Tests\\Unit\\OrderModelTest::it_can_get_payment_method_display_name": 4, "Tests\\Unit\\OrderModelTest::it_can_get_payment_instructions": 4, "Tests\\Unit\\OrderModelTest::it_can_calculate_change_amount": 4, "Tests\\Unit\\OrderModelTest::it_returns_zero_change_when_no_change_required": 4, "Tests\\Unit\\OrderModelTest::it_can_scope_orders_by_payment_method": 4, "Tests\\Unit\\OrderModelTest::it_can_scope_orders_requiring_change": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_belongs_to_a_shop": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_get_enabled_payment_methods": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_check_if_payment_method_is_accepted": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_get_payment_method_details": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_validate_change_amount": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_has_default_values": 3, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_scope_by_shop": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_get_or_create_default_for_shop": 4, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_update_payment_method_acceptance": 4}, "times": {"Tests\\Feature\\DeliveryPaymentMethodsTest::it_can_get_delivery_payment_methods_for_shop": 15.862, "Tests\\Feature\\DeliveryPaymentMethodsTest::it_returns_default_settings_when_no_settings_exist": 0.047, "Tests\\Feature\\DeliveryPaymentMethodsTest::it_returns_404_for_non_existent_shop": 0.087, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_can_create_order_with_cash_delivery_payment": 15.533, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_can_create_order_with_card_delivery_payment": 0.049, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_can_create_order_with_pix_delivery_payment": 0.065, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_validates_change_amount_does_not_exceed_max_limit": 0.056, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_validates_change_amount_is_greater_than_total_price": 0.06, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_validates_payment_method_is_accepted_by_shop": 0.066, "Tests\\Feature\\OrderWithBrazilianPaymentTest::it_requires_change_amount_when_change_is_required": 0.049, "Tests\\Unit\\OrderModelTest::it_has_brazilian_payment_method_constants": 16.179, "Tests\\Unit\\OrderModelTest::it_can_check_if_payment_method_is_delivery_payment": 0.087, "Tests\\Unit\\OrderModelTest::it_can_check_if_payment_method_supports_change": 0.047, "Tests\\Unit\\OrderModelTest::it_can_get_payment_method_display_name": 0.062, "Tests\\Unit\\OrderModelTest::it_can_get_payment_instructions": 0.035, "Tests\\Unit\\OrderModelTest::it_can_calculate_change_amount": 0.169, "Tests\\Unit\\OrderModelTest::it_returns_zero_change_when_no_change_required": 0.051, "Tests\\Unit\\OrderModelTest::it_can_scope_orders_by_payment_method": 0.06, "Tests\\Unit\\OrderModelTest::it_can_scope_orders_requiring_change": 0.042, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_belongs_to_a_shop": 16.202, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_get_enabled_payment_methods": 0.037, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_check_if_payment_method_is_accepted": 0.053, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_get_payment_method_details": 0.062, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_validate_change_amount": 0.046, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_has_default_values": 0.038, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_scope_by_shop": 0.056, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_get_or_create_default_for_shop": 0.048, "Tests\\Unit\\ShopDeliveryPaymentSettingTest::it_can_update_payment_method_acceptance": 0.039}}