<?php

namespace Tests\Unit;

use App\Models\Shop;
use App\Models\ShopDeliveryPaymentSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ShopDeliveryPaymentSettingTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_belongs_to_a_shop()
    {
        $shop = Shop::factory()->create();
        $setting = ShopDeliveryPaymentSetting::factory()->create([
            'shop_id' => $shop->id
        ]);

        $this->assertInstanceOf(Shop::class, $setting->shop);
        $this->assertEquals($shop->id, $setting->shop->id);
    }

    /** @test */
    public function it_can_get_enabled_payment_methods()
    {
        $setting = ShopDeliveryPaymentSetting::factory()->create([
            'accept_cash_delivery' => true,
            'accept_card_delivery' => true,
            'accept_pix_delivery' => false,
            'accept_debit_delivery' => true,
        ]);

        $enabledMethods = $setting->getEnabledPaymentMethods();
        
        $this->assertCount(3, $enabledMethods);
        $this->assertContains('cash_delivery', $enabledMethods);
        $this->assertContains('card_delivery', $enabledMethods);
        $this->assertContains('debit_delivery', $enabledMethods);
        $this->assertNotContains('pix_delivery', $enabledMethods);
    }

    /** @test */
    public function it_can_check_if_payment_method_is_accepted()
    {
        $setting = ShopDeliveryPaymentSetting::factory()->create([
            'accept_cash_delivery' => true,
            'accept_card_delivery' => false,
            'accept_pix_delivery' => true,
            'accept_debit_delivery' => false,
        ]);

        $this->assertTrue($setting->acceptsPaymentMethod('cash_delivery'));
        $this->assertFalse($setting->acceptsPaymentMethod('card_delivery'));
        $this->assertTrue($setting->acceptsPaymentMethod('pix_delivery'));
        $this->assertFalse($setting->acceptsPaymentMethod('debit_delivery'));
        $this->assertFalse($setting->acceptsPaymentMethod('invalid_method'));
    }

    /** @test */
    public function it_can_get_payment_method_details()
    {
        $setting = new ShopDeliveryPaymentSetting();
        
        $details = $setting->getPaymentMethodDetails();
        
        $this->assertIsArray($details);
        $this->assertArrayHasKey('cash_delivery', $details);
        $this->assertArrayHasKey('card_delivery', $details);
        $this->assertArrayHasKey('pix_delivery', $details);
        $this->assertArrayHasKey('debit_delivery', $details);
        
        // Check cash delivery details
        $cashDetails = $details['cash_delivery'];
        $this->assertEquals('cash_delivery', $cashDetails['tag']);
        $this->assertEquals('Dinheiro na Entrega', $cashDetails['name']);
        $this->assertEquals('💵', $cashDetails['icon']);
        $this->assertTrue($cashDetails['supports_change']);
        
        // Check card delivery details
        $cardDetails = $details['card_delivery'];
        $this->assertEquals('card_delivery', $cardDetails['tag']);
        $this->assertEquals('Cartão na Entrega', $cardDetails['name']);
        $this->assertEquals('💳', $cardDetails['icon']);
        $this->assertFalse($cardDetails['supports_change']);
    }

    /** @test */
    public function it_can_validate_change_amount()
    {
        $setting = ShopDeliveryPaymentSetting::factory()->create([
            'max_change_amount' => 100.00
        ]);

        $this->assertTrue($setting->isValidChangeAmount(50.00));
        $this->assertTrue($setting->isValidChangeAmount(100.00));
        $this->assertFalse($setting->isValidChangeAmount(150.00));
        $this->assertFalse($setting->isValidChangeAmount(-10.00));
    }

    /** @test */
    public function it_has_default_values()
    {
        $setting = new ShopDeliveryPaymentSetting();
        
        $this->assertTrue($setting->accept_cash_delivery);
        $this->assertTrue($setting->accept_card_delivery);
        $this->assertTrue($setting->accept_pix_delivery);
        $this->assertTrue($setting->accept_debit_delivery);
        $this->assertEquals(200.00, $setting->max_change_amount);
        $this->assertNull($setting->delivery_payment_instructions);
    }

    /** @test */
    public function it_can_scope_by_shop()
    {
        $shop1 = Shop::factory()->create();
        $shop2 = Shop::factory()->create();
        
        $setting1 = ShopDeliveryPaymentSetting::factory()->create(['shop_id' => $shop1->id]);
        $setting2 = ShopDeliveryPaymentSetting::factory()->create(['shop_id' => $shop2->id]);

        $shop1Settings = ShopDeliveryPaymentSetting::forShop($shop1->id)->get();
        $this->assertCount(1, $shop1Settings);
        $this->assertEquals($setting1->id, $shop1Settings->first()->id);
    }

    /** @test */
    public function it_can_get_or_create_default_for_shop()
    {
        $shop = Shop::factory()->create();
        
        // Should create new setting with defaults
        $setting = ShopDeliveryPaymentSetting::getOrCreateForShop($shop->id);
        
        $this->assertInstanceOf(ShopDeliveryPaymentSetting::class, $setting);
        $this->assertEquals($shop->id, $setting->shop_id);
        $this->assertTrue($setting->accept_cash_delivery);
        $this->assertTrue($setting->accept_card_delivery);
        $this->assertTrue($setting->accept_pix_delivery);
        $this->assertTrue($setting->accept_debit_delivery);
        
        // Should return existing setting
        $existingSetting = ShopDeliveryPaymentSetting::getOrCreateForShop($shop->id);
        $this->assertEquals($setting->id, $existingSetting->id);
    }

    /** @test */
    public function it_can_update_payment_method_acceptance()
    {
        $setting = ShopDeliveryPaymentSetting::factory()->create([
            'accept_cash_delivery' => true,
            'accept_card_delivery' => true,
        ]);

        $setting->updatePaymentMethodAcceptance([
            'cash_delivery' => false,
            'card_delivery' => true,
            'pix_delivery' => true,
        ]);

        $this->assertFalse($setting->fresh()->accept_cash_delivery);
        $this->assertTrue($setting->fresh()->accept_card_delivery);
        $this->assertTrue($setting->fresh()->accept_pix_delivery);
    }
}
