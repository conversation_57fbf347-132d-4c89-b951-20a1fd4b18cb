import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:foodyman/infrastructure/models/data/delivery_payment_method.dart';

part 'delivery_payment_methods_response.freezed.dart';
part 'delivery_payment_methods_response.g.dart';

@freezed
class DeliveryPaymentMethodsResponse with _$DeliveryPaymentMethodsResponse {
  const factory DeliveryPaymentMethodsResponse({
    @Default([]) List<DeliveryPaymentMethod> deliveryPaymentMethods,
    @Default('') String instructions,
    int? shopId,
  }) = _DeliveryPaymentMethodsResponse;

  factory DeliveryPaymentMethodsResponse.fromJson(Map<String, dynamic> json) =>
      _$DeliveryPaymentMethodsResponseFromJson(json);

  const DeliveryPaymentMethodsResponse._();
}
