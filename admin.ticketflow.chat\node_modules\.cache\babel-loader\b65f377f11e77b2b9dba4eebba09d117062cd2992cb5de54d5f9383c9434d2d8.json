{"ast": null, "code": "var _jsxFileName = \"C:\\\\OSPanel\\\\home\\\\admin.ticketflow.chat\\\\src\\\\views\\\\login\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from 'react';\nimport { data } from 'configs/menu-config';\nimport { LockOutlined, MailOutlined } from '@ant-design/icons';\nimport { Button, Card, Col, Descriptions, Form, Input, notification, Row, Typography } from 'antd';\nimport authService from 'services/auth';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUserData } from 'redux/slices/auth';\nimport { fetchRestSettings, fetchSettings } from 'redux/slices/globalSettings';\nimport { useTranslation } from 'react-i18next';\nimport { PROJECT_NAME } from 'configs/app-global';\nimport Recaptcha from 'components/recaptcha';\nimport { setMenu } from 'redux/slices/menu';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst credentials = [{\n  login: '<EMAIL>',\n  password: 'githubit'\n}, {\n  login: '<EMAIL>',\n  password: 'manager'\n}, {\n  login: '<EMAIL>',\n  password: 'seller'\n}, {\n  login: '<EMAIL>',\n  password: 'moderator'\n}, {\n  login: '<EMAIL>',\n  password: 'delivery'\n}];\nconst Login = () => {\n  _s();\n  const {\n    t\n  } = useTranslation();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n  const {\n    settings\n  } = useSelector(state => state.globalSettings);\n  const {\n    user\n  } = useSelector(state => state.auth);\n  const [loading, setLoading] = useState(false);\n  const [recaptcha, setRecaptcha] = useState(null);\n  const isDemo = Boolean(Number(settings === null || settings === void 0 ? void 0 : settings.is_demo));\n\n  // Verificar se o reCAPTCHA deve ser desativado\n  const isRecaptchaDisabled = process.env.REACT_APP_DISABLE_RECAPTCHA === 'true' || window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1' || window.location.hostname.includes('localhost');\n  const handleRecaptchaChange = value => {\n    setRecaptcha(value);\n  };\n  const fetchUserSettings = role => {\n    switch (role) {\n      case 'admin':\n        dispatch(fetchSettings({}));\n        break;\n      case 'seller':\n        dispatch(fetchRestSettings({\n          seller: true\n        }));\n        break;\n      default:\n        dispatch(fetchRestSettings({}));\n    }\n  };\n  const handleLogin = values => {\n    const body = {\n      password: values.password\n    };\n    if (values.email.includes('@')) {\n      body.email = values.email;\n    } else {\n      body.phone = values.email.replace(/[^0-9]/g, '');\n    }\n    setLoading(true);\n    authService.login(body).then(res => {\n      var _res$data$user, _res$data$user$shop, _res$data;\n      const user = {\n        fullName: res.data.user.firstname + ' ' + res.data.user.lastname,\n        role: res.data.user.role,\n        urls: data[res.data.user.role],\n        img: res.data.user.img,\n        token: res.data.access_token,\n        email: res.data.user.email,\n        id: res.data.user.id,\n        shop_id: (_res$data$user = res.data.user) === null || _res$data$user === void 0 ? void 0 : (_res$data$user$shop = _res$data$user.shop) === null || _res$data$user$shop === void 0 ? void 0 : _res$data$user$shop.id\n      };\n      if (user.role === 'waiter') {\n        dispatch(setMenu({\n          icon: 'user',\n          id: 'orders-board',\n          name: 'my.orders',\n          url: 'waiter/orders-board'\n        }));\n      }\n      if ((user === null || user === void 0 ? void 0 : user.role) === 'user') {\n        notification.error({\n          message: t('ERROR_101')\n        });\n        return;\n      }\n      localStorage.setItem('token', res === null || res === void 0 ? void 0 : (_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.access_token);\n      dispatch(setUserData(user));\n      fetchUserSettings(user === null || user === void 0 ? void 0 : user.role);\n    }).finally(() => setLoading(false));\n  };\n  const copyCredentials = (event, item) => {\n    event.preventDefault();\n    form.setFieldsValue({\n      email: item === null || item === void 0 ? void 0 : item.login,\n      password: item === null || item === void 0 ? void 0 : item.password\n    });\n  };\n  useEffect(() => {\n    fetchUserSettings((user === null || user === void 0 ? void 0 : user.role) || '');\n    return () => {};\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container d-flex flex-column justify-content-center h-100 align-items-end\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"center\",\n        children: /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"card\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"my-4 pl-4 pr-4 w-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"app-brand text-center\",\n                children: /*#__PURE__*/_jsxDEV(Title, {\n                  className: \"brand-logo\",\n                  children: settings.title || PROJECT_NAME\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Row, {\n                justify: \"center\",\n                children: /*#__PURE__*/_jsxDEV(Col, {\n                  children: /*#__PURE__*/_jsxDEV(Form, {\n                    name: \"login-form\",\n                    layout: \"vertical\",\n                    form: form,\n                    onFinish: handleLogin,\n                    style: {\n                      width: '420px'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"email\",\n                      label: \"Email\",\n                      rules: [{\n                        required: true,\n                        message: 'Please input your Email!'\n                      }],\n                      children: /*#__PURE__*/_jsxDEV(Input, {\n                        prefix: /*#__PURE__*/_jsxDEV(MailOutlined, {\n                          className: \"site-form-item-icon\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 173,\n                          columnNumber: 29\n                        }, this),\n                        placeholder: \"Email or phone\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 171,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                      name: \"password\",\n                      label: \"Password\",\n                      rules: [{\n                        required: true,\n                        message: 'Please input your password!'\n                      }],\n                      children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                        prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {\n                          className: \"site-form-item-icon\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 190,\n                          columnNumber: 29\n                        }, this),\n                        placeholder: \"Password\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 188,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 178,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Recaptcha, {\n                      onChange: handleRecaptchaChange\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 195,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n                      className: \"login-input mt-4\",\n                      children: /*#__PURE__*/_jsxDEV(Button, {\n                        type: \"primary\",\n                        htmlType: \"submit\",\n                        className: \"login-form-button\",\n                        loading: loading,\n                        disabled: !Boolean(recaptcha) && !isRecaptchaDisabled,\n                        children: t('Login')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 197,\n                        columnNumber: 25\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 196,\n                      columnNumber: 23\n                    }, this), isDemo && /*#__PURE__*/_jsxDEV(Descriptions, {\n                      bordered: true,\n                      size: \"small\",\n                      children: credentials.map((item, idx) => /*#__PURE__*/_jsxDEV(React.Fragment, {\n                        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                          span: 2,\n                          label: item.login,\n                          children: item.password\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 211,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                          span: 1,\n                          children: /*#__PURE__*/_jsxDEV(\"a\", {\n                            href: \"/\",\n                            className: \"copy-link\",\n                            onClick: event => copyCredentials(event, item),\n                            children: t('Copy')\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 215,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 214,\n                          columnNumber: 31\n                        }, this)]\n                      }, idx, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 210,\n                        columnNumber: 29\n                      }, this))\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 208,\n                      columnNumber: 25\n                    }, this), ' ', !isDemo && process.env.REACT_APP_IS_DEMO === 'true' && /*#__PURE__*/_jsxDEV(Descriptions, {\n                      bordered: true,\n                      size: \"small\",\n                      children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                        span: 2,\n                        label: credentials[0].login,\n                        children: credentials[0].password\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 231,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n                        span: 1,\n                        children: /*#__PURE__*/_jsxDEV(\"a\", {\n                          href: \"/\",\n                          className: \"copy-link\",\n                          onClick: event => copyCredentials(event, credentials[0]),\n                          children: t('Copy')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 238,\n                          columnNumber: 29\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 237,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 230,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"w3FMFFjKySUZfPgv0cNrwBfHTXk=\", false, function () {\n  return [useTranslation, useDispatch, Form.useForm, useSelector, useSelector];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "data", "LockOutlined", "MailOutlined", "<PERSON><PERSON>", "Card", "Col", "Descriptions", "Form", "Input", "notification", "Row", "Typography", "authService", "useDispatch", "useSelector", "setUserData", "fetchRestSettings", "fetchSettings", "useTranslation", "PROJECT_NAME", "<PERSON><PERSON><PERSON><PERSON>", "setMenu", "jsxDEV", "_jsxDEV", "Title", "credentials", "login", "password", "<PERSON><PERSON>", "_s", "t", "dispatch", "form", "useForm", "settings", "state", "globalSettings", "user", "auth", "loading", "setLoading", "recaptcha", "setRecaptcha", "isDemo", "Boolean", "Number", "is_demo", "isRecaptchaDisabled", "process", "env", "REACT_APP_DISABLE_RECAPTCHA", "window", "location", "hostname", "includes", "handleRecaptchaChange", "value", "fetchUserSettings", "role", "seller", "handleLogin", "values", "body", "email", "phone", "replace", "then", "res", "_res$data$user", "_res$data$user$shop", "_res$data", "fullName", "firstname", "lastname", "urls", "img", "token", "access_token", "id", "shop_id", "shop", "icon", "name", "url", "error", "message", "localStorage", "setItem", "finally", "copyCredentials", "event", "item", "preventDefault", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "className", "children", "justify", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "layout", "onFinish", "style", "width", "<PERSON><PERSON>", "label", "rules", "required", "prefix", "placeholder", "Password", "onChange", "type", "htmlType", "disabled", "bordered", "size", "map", "idx", "Fragment", "span", "href", "onClick", "REACT_APP_IS_DEMO", "_c", "$RefreshReg$"], "sources": ["C:/OSPanel/home/<USER>/src/views/login/index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { data } from 'configs/menu-config';\nimport { LockOutlined, MailOutlined } from '@ant-design/icons';\nimport {\n  Button,\n  Card,\n  Col,\n  Descriptions,\n  Form,\n  Input,\n  notification,\n  Row,\n  Typography,\n} from 'antd';\nimport authService from 'services/auth';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUserData } from 'redux/slices/auth';\nimport { fetchRestSettings, fetchSettings } from 'redux/slices/globalSettings';\nimport { useTranslation } from 'react-i18next';\nimport { PROJECT_NAME } from 'configs/app-global';\nimport Recaptcha from 'components/recaptcha';\nimport { setMenu } from 'redux/slices/menu';\nconst { Title } = Typography;\n\nconst credentials = [\n  {\n    login: '<EMAIL>',\n    password: 'githubit',\n  },\n  {\n    login: '<EMAIL>',\n    password: 'manager',\n  },\n  {\n    login: '<EMAIL>',\n    password: 'seller',\n  },\n  {\n    login: '<EMAIL>',\n    password: 'moderator',\n  },\n  {\n    login: '<EMAIL>',\n    password: 'delivery',\n  },\n];\n\nconst Login = () => {\n  const { t } = useTranslation();\n  const dispatch = useDispatch();\n  const [form] = Form.useForm();\n\n  const { settings } = useSelector((state) => state.globalSettings);\n  const { user } = useSelector((state) => state.auth);\n\n  const [loading, setLoading] = useState(false);\n  const [recaptcha, setRecaptcha] = useState(null);\n\n  const isDemo = Boolean(Number(settings?.is_demo));\n  \n  // Verificar se o reCAPTCHA deve ser desativado\n  const isRecaptchaDisabled = process.env.REACT_APP_DISABLE_RECAPTCHA === 'true' ||\n                             window.location.hostname === 'localhost' || \n                             window.location.hostname === '127.0.0.1' ||\n                             window.location.hostname.includes('localhost');\n\n  const handleRecaptchaChange = (value) => {\n    setRecaptcha(value);\n  };\n\n  const fetchUserSettings = (role) => {\n    switch (role) {\n      case 'admin':\n        dispatch(fetchSettings({}));\n        break;\n      case 'seller':\n        dispatch(fetchRestSettings({ seller: true }));\n        break;\n      default:\n        dispatch(fetchRestSettings({}));\n    }\n  };\n\n  const handleLogin = (values) => {\n    const body = {\n      password: values.password,\n    };\n    if (values.email.includes('@')) {\n      body.email = values.email;\n    } else {\n      body.phone = values.email.replace(/[^0-9]/g, '');\n    }\n    setLoading(true);\n    authService\n      .login(body)\n      .then((res) => {\n        const user = {\n          fullName: res.data.user.firstname + ' ' + res.data.user.lastname,\n          role: res.data.user.role,\n          urls: data[res.data.user.role],\n          img: res.data.user.img,\n          token: res.data.access_token,\n          email: res.data.user.email,\n          id: res.data.user.id,\n          shop_id: res.data.user?.shop?.id,\n        };\n        if (user.role === 'waiter') {\n          dispatch(\n            setMenu({\n              icon: 'user',\n              id: 'orders-board',\n              name: 'my.orders',\n              url: 'waiter/orders-board',\n            }),\n          );\n        }\n        if (user?.role === 'user') {\n          notification.error({\n            message: t('ERROR_101'),\n          });\n          return;\n        }\n        localStorage.setItem('token', res?.data?.access_token);\n        dispatch(setUserData(user));\n        fetchUserSettings(user?.role);\n      })\n      .finally(() => setLoading(false));\n  };\n\n  const copyCredentials = (event, item) => {\n    event.preventDefault();\n    form.setFieldsValue({ email: item?.login, password: item?.password });\n  };\n\n  useEffect(() => {\n    fetchUserSettings(user?.role || '');\n    return () => {};\n  }, []);\n\n  return (\n    <div className='login-container'>\n      <div className='container d-flex flex-column justify-content-center h-100 align-items-end'>\n        <Row justify='center'>\n          <Col>\n            <Card className='card'>\n              <div className='my-4 pl-4 pr-4 w-100'>\n                <div className='app-brand text-center'>\n                  <Title className='brand-logo'>\n                    {settings.title || PROJECT_NAME}\n                  </Title>\n                </div>\n                <Row justify='center'>\n                  <Col>\n                    <Form\n                      name='login-form'\n                      layout='vertical'\n                      form={form}\n                      onFinish={handleLogin}\n                      style={{ width: '420px' }}\n                    >\n                      <Form.Item\n                        name='email'\n                        label='Email'\n                        rules={[\n                          {\n                            required: true,\n                            message: 'Please input your Email!',\n                          },\n                        ]}\n                      >\n                        <Input\n                          prefix={\n                            <MailOutlined className='site-form-item-icon' />\n                          }\n                          placeholder='Email or phone'\n                        />\n                      </Form.Item>\n                      <Form.Item\n                        name='password'\n                        label='Password'\n                        rules={[\n                          {\n                            required: true,\n                            message: 'Please input your password!',\n                          },\n                        ]}\n                      >\n                        <Input.Password\n                          prefix={\n                            <LockOutlined className='site-form-item-icon' />\n                          }\n                          placeholder='Password'\n                        />\n                      </Form.Item>\n                      <Recaptcha onChange={handleRecaptchaChange} />\n                      <Form.Item className='login-input mt-4'>\n                        <Button\n                          type='primary'\n                          htmlType='submit'\n                          className='login-form-button'\n                          loading={loading}\n                          disabled={!Boolean(recaptcha) && !isRecaptchaDisabled}\n                        >\n                          {t('Login')}\n                        </Button>\n                      </Form.Item>\n                      {isDemo && (\n                        <Descriptions bordered size='small'>\n                          {credentials.map((item, idx) => (\n                            <React.Fragment key={idx}>\n                              <Descriptions.Item span={2} label={item.login}>\n                                {item.password}\n                              </Descriptions.Item>\n                              <Descriptions.Item span={1}>\n                                <a\n                                  href='/'\n                                  className='copy-link'\n                                  onClick={(event) =>\n                                    copyCredentials(event, item)\n                                  }\n                                >\n                                  {t('Copy')}\n                                </a>\n                              </Descriptions.Item>\n                            </React.Fragment>\n                          ))}\n                        </Descriptions>\n                      )}{' '}\n                      {!isDemo && process.env.REACT_APP_IS_DEMO === 'true' && (\n                        <Descriptions bordered size='small'>\n                          <Descriptions.Item\n                            span={2}\n                            label={credentials[0].login}\n                          >\n                            {credentials[0].password}\n                          </Descriptions.Item>\n                          <Descriptions.Item span={1}>\n                            <a\n                              href='/'\n                              className='copy-link'\n                              onClick={(event) =>\n                                copyCredentials(event, credentials[0])\n                              }\n                            >\n                              {t('Copy')}\n                            </a>\n                          </Descriptions.Item>\n                        </Descriptions>\n                      )}\n                    </Form>\n                  </Col>\n                </Row>\n              </div>\n            </Card>\n          </Col>\n        </Row>\n      </div>\n    </div>\n  );\n};\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,IAAI,QAAQ,qBAAqB;AAC1C,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SACEC,MAAM,EACNC,IAAI,EACJC,GAAG,EACHC,YAAY,EACZC,IAAI,EACJC,KAAK,EACLC,YAAY,EACZC,GAAG,EACHC,UAAU,QACL,MAAM;AACb,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,iBAAiB,EAAEC,aAAa,QAAQ,6BAA6B;AAC9E,SAASC,cAAc,QAAQ,eAAe;AAC9C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,SAASC,OAAO,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAC5C,MAAM;EAAEC;AAAM,CAAC,GAAGb,UAAU;AAE5B,MAAMc,WAAW,GAAG,CAClB;EACEC,KAAK,EAAE,oBAAoB;EAC3BC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,KAAK,EAAE,sBAAsB;EAC7BC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,KAAK,EAAE,wBAAwB;EAC/BC,QAAQ,EAAE;AACZ,CAAC,EACD;EACED,KAAK,EAAE,uBAAuB;EAC9BC,QAAQ,EAAE;AACZ,CAAC,CACF;AAED,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM;IAAEC;EAAE,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAC9B,MAAMa,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACmB,IAAI,CAAC,GAAGzB,IAAI,CAAC0B,OAAO,CAAC,CAAC;EAE7B,MAAM;IAAEC;EAAS,CAAC,GAAGpB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACC,cAAc,CAAC;EACjE,MAAM;IAAEC;EAAK,CAAC,GAAGvB,WAAW,CAAEqB,KAAK,IAAKA,KAAK,CAACG,IAAI,CAAC;EAEnD,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAEhD,MAAM6C,MAAM,GAAGC,OAAO,CAACC,MAAM,CAACX,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEY,OAAO,CAAC,CAAC;;EAEjD;EACA,MAAMC,mBAAmB,GAAGC,OAAO,CAACC,GAAG,CAACC,2BAA2B,KAAK,MAAM,IACnDC,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,IACxCF,MAAM,CAACC,QAAQ,CAACC,QAAQ,KAAK,WAAW,IACxCF,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAC;EAEzE,MAAMC,qBAAqB,GAAIC,KAAK,IAAK;IACvCd,YAAY,CAACc,KAAK,CAAC;EACrB,CAAC;EAED,MAAMC,iBAAiB,GAAIC,IAAI,IAAK;IAClC,QAAQA,IAAI;MACV,KAAK,OAAO;QACV3B,QAAQ,CAACd,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B;MACF,KAAK,QAAQ;QACXc,QAAQ,CAACf,iBAAiB,CAAC;UAAE2C,MAAM,EAAE;QAAK,CAAC,CAAC,CAAC;QAC7C;MACF;QACE5B,QAAQ,CAACf,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC;IACnC;EACF,CAAC;EAED,MAAM4C,WAAW,GAAIC,MAAM,IAAK;IAC9B,MAAMC,IAAI,GAAG;MACXnC,QAAQ,EAAEkC,MAAM,CAAClC;IACnB,CAAC;IACD,IAAIkC,MAAM,CAACE,KAAK,CAACT,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC9BQ,IAAI,CAACC,KAAK,GAAGF,MAAM,CAACE,KAAK;IAC3B,CAAC,MAAM;MACLD,IAAI,CAACE,KAAK,GAAGH,MAAM,CAACE,KAAK,CAACE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAClD;IACAzB,UAAU,CAAC,IAAI,CAAC;IAChB5B,WAAW,CACRc,KAAK,CAACoC,IAAI,CAAC,CACXI,IAAI,CAAEC,GAAG,IAAK;MAAA,IAAAC,cAAA,EAAAC,mBAAA,EAAAC,SAAA;MACb,MAAMjC,IAAI,GAAG;QACXkC,QAAQ,EAAEJ,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAACmC,SAAS,GAAG,GAAG,GAAGL,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAACoC,QAAQ;QAChEf,IAAI,EAAES,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAACqB,IAAI;QACxBgB,IAAI,EAAE1E,IAAI,CAACmE,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAACqB,IAAI,CAAC;QAC9BiB,GAAG,EAAER,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAACsC,GAAG;QACtBC,KAAK,EAAET,GAAG,CAACnE,IAAI,CAAC6E,YAAY;QAC5Bd,KAAK,EAAEI,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAAC0B,KAAK;QAC1Be,EAAE,EAAEX,GAAG,CAACnE,IAAI,CAACqC,IAAI,CAACyC,EAAE;QACpBC,OAAO,GAAAX,cAAA,GAAED,GAAG,CAACnE,IAAI,CAACqC,IAAI,cAAA+B,cAAA,wBAAAC,mBAAA,GAAbD,cAAA,CAAeY,IAAI,cAAAX,mBAAA,uBAAnBA,mBAAA,CAAqBS;MAChC,CAAC;MACD,IAAIzC,IAAI,CAACqB,IAAI,KAAK,QAAQ,EAAE;QAC1B3B,QAAQ,CACNV,OAAO,CAAC;UACN4D,IAAI,EAAE,MAAM;UACZH,EAAE,EAAE,cAAc;UAClBI,IAAI,EAAE,WAAW;UACjBC,GAAG,EAAE;QACP,CAAC,CACH,CAAC;MACH;MACA,IAAI,CAAA9C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,MAAK,MAAM,EAAE;QACzBjD,YAAY,CAAC2E,KAAK,CAAC;UACjBC,OAAO,EAAEvD,CAAC,CAAC,WAAW;QACxB,CAAC,CAAC;QACF;MACF;MACAwD,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEpB,GAAG,aAAHA,GAAG,wBAAAG,SAAA,GAAHH,GAAG,CAAEnE,IAAI,cAAAsE,SAAA,uBAATA,SAAA,CAAWO,YAAY,CAAC;MACtD9C,QAAQ,CAAChB,WAAW,CAACsB,IAAI,CAAC,CAAC;MAC3BoB,iBAAiB,CAACpB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,CAAC;IAC/B,CAAC,CAAC,CACD8B,OAAO,CAAC,MAAMhD,UAAU,CAAC,KAAK,CAAC,CAAC;EACrC,CAAC;EAED,MAAMiD,eAAe,GAAGA,CAACC,KAAK,EAAEC,IAAI,KAAK;IACvCD,KAAK,CAACE,cAAc,CAAC,CAAC;IACtB5D,IAAI,CAAC6D,cAAc,CAAC;MAAE9B,KAAK,EAAE4B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEjE,KAAK;MAAEC,QAAQ,EAAEgE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEhE;IAAS,CAAC,CAAC;EACvE,CAAC;EAED9B,SAAS,CAAC,MAAM;IACd4D,iBAAiB,CAAC,CAAApB,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqB,IAAI,KAAI,EAAE,CAAC;IACnC,OAAO,MAAM,CAAC,CAAC;EACjB,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEnC,OAAA;IAAKuE,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BxE,OAAA;MAAKuE,SAAS,EAAC,2EAA2E;MAAAC,QAAA,eACxFxE,OAAA,CAACb,GAAG;QAACsF,OAAO,EAAC,QAAQ;QAAAD,QAAA,eACnBxE,OAAA,CAAClB,GAAG;UAAA0F,QAAA,eACFxE,OAAA,CAACnB,IAAI;YAAC0F,SAAS,EAAC,MAAM;YAAAC,QAAA,eACpBxE,OAAA;cAAKuE,SAAS,EAAC,sBAAsB;cAAAC,QAAA,gBACnCxE,OAAA;gBAAKuE,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,eACpCxE,OAAA,CAACC,KAAK;kBAACsE,SAAS,EAAC,YAAY;kBAAAC,QAAA,EAC1B7D,QAAQ,CAAC+D,KAAK,IAAI9E;gBAAY;kBAAA+E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN9E,OAAA,CAACb,GAAG;gBAACsF,OAAO,EAAC,QAAQ;gBAAAD,QAAA,eACnBxE,OAAA,CAAClB,GAAG;kBAAA0F,QAAA,eACFxE,OAAA,CAAChB,IAAI;oBACH2E,IAAI,EAAC,YAAY;oBACjBoB,MAAM,EAAC,UAAU;oBACjBtE,IAAI,EAAEA,IAAK;oBACXuE,QAAQ,EAAE3C,WAAY;oBACtB4C,KAAK,EAAE;sBAAEC,KAAK,EAAE;oBAAQ,CAAE;oBAAAV,QAAA,gBAE1BxE,OAAA,CAAChB,IAAI,CAACmG,IAAI;sBACRxB,IAAI,EAAC,OAAO;sBACZyB,KAAK,EAAC,OAAO;sBACbC,KAAK,EAAE,CACL;wBACEC,QAAQ,EAAE,IAAI;wBACdxB,OAAO,EAAE;sBACX,CAAC,CACD;sBAAAU,QAAA,eAEFxE,OAAA,CAACf,KAAK;wBACJsG,MAAM,eACJvF,OAAA,CAACrB,YAAY;0BAAC4F,SAAS,EAAC;wBAAqB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAChD;wBACDU,WAAW,EAAC;sBAAgB;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZ9E,OAAA,CAAChB,IAAI,CAACmG,IAAI;sBACRxB,IAAI,EAAC,UAAU;sBACfyB,KAAK,EAAC,UAAU;sBAChBC,KAAK,EAAE,CACL;wBACEC,QAAQ,EAAE,IAAI;wBACdxB,OAAO,EAAE;sBACX,CAAC,CACD;sBAAAU,QAAA,eAEFxE,OAAA,CAACf,KAAK,CAACwG,QAAQ;wBACbF,MAAM,eACJvF,OAAA,CAACtB,YAAY;0BAAC6F,SAAS,EAAC;wBAAqB;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAChD;wBACDU,WAAW,EAAC;sBAAU;wBAAAb,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACO,CAAC,eACZ9E,OAAA,CAACH,SAAS;sBAAC6F,QAAQ,EAAE1D;oBAAsB;sBAAA2C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAC9C9E,OAAA,CAAChB,IAAI,CAACmG,IAAI;sBAACZ,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,eACrCxE,OAAA,CAACpB,MAAM;wBACL+G,IAAI,EAAC,SAAS;wBACdC,QAAQ,EAAC,QAAQ;wBACjBrB,SAAS,EAAC,mBAAmB;wBAC7BvD,OAAO,EAAEA,OAAQ;wBACjB6E,QAAQ,EAAE,CAACxE,OAAO,CAACH,SAAS,CAAC,IAAI,CAACM,mBAAoB;wBAAAgD,QAAA,EAErDjE,CAAC,CAAC,OAAO;sBAAC;wBAAAoE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACL;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACA,CAAC,EACX1D,MAAM,iBACLpB,OAAA,CAACjB,YAAY;sBAAC+G,QAAQ;sBAACC,IAAI,EAAC,OAAO;sBAAAvB,QAAA,EAChCtE,WAAW,CAAC8F,GAAG,CAAC,CAAC5B,IAAI,EAAE6B,GAAG,kBACzBjG,OAAA,CAAC3B,KAAK,CAAC6H,QAAQ;wBAAA1B,QAAA,gBACbxE,OAAA,CAACjB,YAAY,CAACoG,IAAI;0BAACgB,IAAI,EAAE,CAAE;0BAACf,KAAK,EAAEhB,IAAI,CAACjE,KAAM;0BAAAqE,QAAA,EAC3CJ,IAAI,CAAChE;wBAAQ;0BAAAuE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACG,CAAC,eACpB9E,OAAA,CAACjB,YAAY,CAACoG,IAAI;0BAACgB,IAAI,EAAE,CAAE;0BAAA3B,QAAA,eACzBxE,OAAA;4BACEoG,IAAI,EAAC,GAAG;4BACR7B,SAAS,EAAC,WAAW;4BACrB8B,OAAO,EAAGlC,KAAK,IACbD,eAAe,CAACC,KAAK,EAAEC,IAAI,CAC5B;4BAAAI,QAAA,EAEAjE,CAAC,CAAC,MAAM;0BAAC;4BAAAoE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACa,CAAC;sBAAA,GAdDmB,GAAG;wBAAAtB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAeR,CACjB;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACU,CACf,EAAE,GAAG,EACL,CAAC1D,MAAM,IAAIK,OAAO,CAACC,GAAG,CAAC4E,iBAAiB,KAAK,MAAM,iBAClDtG,OAAA,CAACjB,YAAY;sBAAC+G,QAAQ;sBAACC,IAAI,EAAC,OAAO;sBAAAvB,QAAA,gBACjCxE,OAAA,CAACjB,YAAY,CAACoG,IAAI;wBAChBgB,IAAI,EAAE,CAAE;wBACRf,KAAK,EAAElF,WAAW,CAAC,CAAC,CAAC,CAACC,KAAM;wBAAAqE,QAAA,EAE3BtE,WAAW,CAAC,CAAC,CAAC,CAACE;sBAAQ;wBAAAuE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC,eACpB9E,OAAA,CAACjB,YAAY,CAACoG,IAAI;wBAACgB,IAAI,EAAE,CAAE;wBAAA3B,QAAA,eACzBxE,OAAA;0BACEoG,IAAI,EAAC,GAAG;0BACR7B,SAAS,EAAC,WAAW;0BACrB8B,OAAO,EAAGlC,KAAK,IACbD,eAAe,CAACC,KAAK,EAAEjE,WAAW,CAAC,CAAC,CAAC,CACtC;0BAAAsE,QAAA,EAEAjE,CAAC,CAAC,MAAM;wBAAC;0BAAAoE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACT;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACa,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CACf;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxE,EAAA,CApNID,KAAK;EAAA,QACKV,cAAc,EACXL,WAAW,EACbN,IAAI,CAAC0B,OAAO,EAENnB,WAAW,EACfA,WAAW;AAAA;AAAAgH,EAAA,GANxBlG,KAAK;AAqNX,eAAeA,KAAK;AAAC,IAAAkG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}