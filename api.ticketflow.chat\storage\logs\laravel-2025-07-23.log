[2025-07-23 19:21:48] testing.ERROR: No query results for model [App\Models\Shop] 999 {"code":0,"message":"No query results for model [App\\Models\\Shop] 999","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php","line":444} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (101, e747e589-cd5f-407d-889f-cfd3a4558160, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (101, e747e589-cd5f-407d-889f-cfd3a4558160, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 101)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 101))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (102, 838b212b-d3e2-4021-9e42-92bfb5ff36f1, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (102, 838b212b-d3e2-4021-9e42-92bfb5ff36f1, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 102)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 102))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (103, 57fd2a29-2cb6-402b-a3e2-3eee886f63c2, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (103, 57fd2a29-2cb6-402b-a3e2-3eee886f63c2, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 103)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 103))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (104, dd22d7bf-8251-4684-812f-f2c141950eea, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (104, dd22d7bf-8251-4684-812f-f2c141950eea, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 104)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 104))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (105, e8a97d07-a823-43a0-b4e4-fef95341dc60, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (105, e8a97d07-a823-43a0-b4e4-fef95341dc60, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 105)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 105))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (106, c47c7a63-15b5-49b3-b21f-1248aeea8ae6, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (106, c47c7a63-15b5-49b3-b21f-1248aeea8ae6, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 106)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 106))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (107, bb25176d-193f-4eb9-9e9a-9bb51298d615, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (107, bb25176d-193f-4eb9-9e9a-9bb51298d615, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 107)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 107))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (108, acbf69d3-1fb5-40c6-999d-14ad84a43b84, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (108, acbf69d3-1fb5-40c6-999d-14ad84a43b84, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 108)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 108))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (109, 38c87a44-9656-4322-befb-58f36569e11e, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (109, 38c87a44-9656-4322-befb-58f36569e11e, ?, 0, ?, 2025-07-23 19:35:17, 2025-07-23 19:35:17))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 109)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 109))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:17] local1.ERROR: There is no role named `admin`. {"code":0,"message":"There is no role named `admin`.","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\RoleDoesNotExist.php","line":11} 
[2025-07-23 19:35:17] local1.ERROR: There is no role named `deliveryman`. {"code":0,"message":"There is no role named `deliveryman`.","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\RoleDoesNotExist.php","line":11} 
[2025-07-23 19:35:17] local1.ERROR: There is no role named `cook`. {"code":0,"message":"There is no role named `cook`.","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\RoleDoesNotExist.php","line":11} 
[2025-07-23 19:35:17] local1.ERROR: There is no role named `moderator`. {"code":0,"message":"There is no role named `moderator`.","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\spatie\\laravel-permission\\src\\Exceptions\\RoleDoesNotExist.php","line":11} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (101, c166384f-51f5-479e-9bd3-49202edbd557, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (101, c166384f-51f5-479e-9bd3-49202edbd557, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 101)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 101))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (102, cb11a67f-35e0-4bf6-9cc0-5663a25d5f05, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (102, cb11a67f-35e0-4bf6-9cc0-5663a25d5f05, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 102)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 102))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (103, d4fd5e97-efdd-428e-9ad5-583075bc156b, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (103, d4fd5e97-efdd-428e-9ad5-583075bc156b, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 103)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 103))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (104, 3b8e4bcc-49dc-4c8a-8cba-1cfb15ee8797, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (104, 3b8e4bcc-49dc-4c8a-8cba-1cfb15ee8797, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 104)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 104))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (105, 18aee182-2d7e-47ff-866f-444d1de2954e, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (105, 18aee182-2d7e-47ff-866f-444d1de2954e, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 105)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 105))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (106, ac703a47-a022-4c43-b70d-362ad53534e9, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (106, ac703a47-a022-4c43-b70d-362ad53534e9, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 106)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 106))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (107, ba1012c0-c731-4e0a-b242-88159fe8d7a8, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (107, ba1012c0-c731-4e0a-b242-88159fe8d7a8, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 107)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 107))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (108, f4b4aa26-93c5-4f8a-8f74-914956ec92e3, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (108, f4b4aa26-93c5-4f8a-8f74-914956ec92e3, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 108)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 108))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (109, 08b55410-0884-4881-a8ae-05f3a961fe5e, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'currency_id' cannot be null (SQL: insert into `wallets` (`user_id`, `uuid`, `currency_id`, `price`, `deleted_at`, `updated_at`, `created_at`) values (109, 08b55410-0884-4881-a8ae-05f3a961fe5e, ?, 0, ?, 2025-07-23 19:35:28, 2025-07-23 19:35:28))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
[2025-07-23 19:35:28] local1.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 109)) {"code":"23000","message":"SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails (`foodyman`.`notification_user`, CONSTRAINT `notification_user_notification_id_foreign` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) (SQL: insert into `notification_user` (`notification_id`, `user_id`) values (, 109))","file":"C:\\OSPanel\\home\\api.ticketflow.chat\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php","line":712} 
