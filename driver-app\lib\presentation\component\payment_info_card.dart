import 'package:flutter/material.dart';
import 'package:flutter_remix/flutter_remix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:driver/infrastructure/models/data/order_detail.dart';
import 'package:driver/infrastructure/services/app_helpers.dart';
import 'package:driver/infrastructure/services/tr_keys.dart';
import 'package:driver/presentation/styles/style.dart';

class PaymentInfoCard extends StatelessWidget {
  final OrderDetailData order;

  const PaymentInfoCard({
    Key? key,
    required this.order,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Style.white,
        borderRadius: BorderRadius.circular(10.r),
      ),
      padding: EdgeInsets.all(16.r),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                FlutterRemix.bank_card_2_line,
                size: 18.sp,
                color: Style.black,
              ),
              8.horizontalSpace,
              Text(
                AppHelpers.getTranslation(TrKeys.paymentInformation),
                style: Style.interSemi(
                  size: 14.sp,
                  color: Style.black,
                  letterSpacing: -0.3,
                ),
              ),
            ],
          ),

          12.verticalSpace,

          // Payment Method
          _buildPaymentMethodRow(),

          if (_isDeliveryPayment()) ...[
            8.verticalSpace,
            _buildDeliveryPaymentInfo(),
          ],

          8.verticalSpace,

          // Total and Delivery Fee
          Row(
            children: [
              Expanded(
                child: _buildInfoItem(
                  icon: "assets/svg/cutter.svg",
                  label: AppHelpers.getTranslation(TrKeys.total),
                  value: AppHelpers.numberFormat(number: order.totalPrice ?? 0),
                ),
              ),
              16.horizontalSpace,
              Expanded(
                child: _buildInfoItem(
                  icon: FlutterRemix.takeaway_fill,
                  label: AppHelpers.getTranslation(TrKeys.deliveryFee),
                  value: AppHelpers.numberFormat(number: order.deliveryFee ?? 0),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodRow() {
    return Row(
      children: [
        _getPaymentMethodIcon(),
        8.horizontalSpace,
        Expanded(
          child: Text(
            _getPaymentMethodName(),
            style: Style.interMedium(
              size: 13.sp,
              color: Style.black,
            ),
          ),
        ),
        if (_isDeliveryPayment())
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
            decoration: BoxDecoration(
              color: Style.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Text(
              AppHelpers.getTranslation(TrKeys.payOnDelivery),
              style: Style.interMedium(
                size: 10.sp,
                color: Style.primary,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildDeliveryPaymentInfo() {
    if (order.paymentMethod == 'cash_delivery' && (order.changeRequired ?? false)) {
      return Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Style.warning.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Style.warning.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(
              FlutterRemix.alert_line,
              size: 16.sp,
              color: Style.warning,
            ),
            8.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    AppHelpers.getTranslation(TrKeys.changeRequired),
                    style: Style.interSemi(
                      size: 12.sp,
                      color: Style.warning,
                    ),
                  ),
                  2.verticalSpace,
                  Text(
                    "${AppHelpers.getTranslation(TrKeys.changeFor)} ${AppHelpers.numberFormat(number: order.changeAmount ?? 0)}",
                    style: Style.interMedium(
                      size: 11.sp,
                      color: Style.black,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    } else if (order.paymentMethod == 'cash_delivery') {
      return Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Style.success.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Style.success.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(
              FlutterRemix.check_line,
              size: 16.sp,
              color: Style.success,
            ),
            8.horizontalSpace,
            Text(
              AppHelpers.getTranslation(TrKeys.exactAmount),
              style: Style.interMedium(
                size: 12.sp,
                color: Style.success,
              ),
            ),
          ],
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.all(12.r),
        decoration: BoxDecoration(
          color: Style.primary.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: Style.primary.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(
              FlutterRemix.bank_card_line,
              size: 16.sp,
              color: Style.primary,
            ),
            8.horizontalSpace,
            Text(
              _getPaymentInstructions(),
              style: Style.interMedium(
                size: 12.sp,
                color: Style.black,
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _buildInfoItem({
    dynamic icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        if (icon is String)
          SvgPicture.asset(
            icon,
            width: 16.r,
            color: Style.black,
          )
        else
          Icon(
            icon,
            size: 16.sp,
            color: Style.black,
          ),
        6.horizontalSpace,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Style.interNormal(
                  size: 10.sp,
                  color: Style.textGrey,
                ),
              ),
              Text(
                value,
                style: Style.interSemi(
                  size: 12.sp,
                  color: Style.black,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool _isDeliveryPayment() {
    return ['cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery']
        .contains(order.paymentMethod);
  }

  Widget _getPaymentMethodIcon() {
    switch (order.paymentMethod) {
      case 'cash_delivery':
        return Text('💵', style: TextStyle(fontSize: 16.sp));
      case 'card_delivery':
        return Icon(FlutterRemix.bank_card_line, size: 16.sp, color: Style.black);
      case 'pix_delivery':
        return Text('📱', style: TextStyle(fontSize: 16.sp));
      case 'debit_delivery':
        return Icon(FlutterRemix.bank_card_2_line, size: 16.sp, color: Style.black);
      default:
        return Icon(FlutterRemix.bank_card_line, size: 16.sp, color: Style.black);
    }
  }

  String _getPaymentMethodName() {
    switch (order.paymentMethod) {
      case 'cash_delivery':
        return AppHelpers.getTranslation(TrKeys.cashDelivery);
      case 'card_delivery':
        return AppHelpers.getTranslation(TrKeys.cardDelivery);
      case 'pix_delivery':
        return AppHelpers.getTranslation(TrKeys.pixDelivery);
      case 'debit_delivery':
        return AppHelpers.getTranslation(TrKeys.debitDelivery);
      default:
        return order.transaction?.paymentSystem?.tag ?? 
               AppHelpers.getTranslation(TrKeys.onlinePayment);
    }
  }

  String _getPaymentInstructions() {
    switch (order.paymentMethod) {
      case 'card_delivery':
        return AppHelpers.getTranslation(TrKeys.useCardMachine);
      case 'pix_delivery':
        return AppHelpers.getTranslation(TrKeys.usePixTerminal);
      case 'debit_delivery':
        return AppHelpers.getTranslation(TrKeys.useDebitMachine);
      default:
        return '';
    }
  }
}
