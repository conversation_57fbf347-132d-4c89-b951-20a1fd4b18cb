# Implementação de Métodos de Pagamento Brasileiros

## Visão Geral

Este documento descreve a implementação completa dos métodos de pagamento brasileiros para entrega no sistema de delivery. A implementação inclui suporte para pagamento em dinheiro, cartão, PIX e débito na entrega, com funcionalidades específicas como controle de troco e instruções de pagamento.

## Métodos de Pagamento Implementados

### 1. Dinheiro na Entrega (cash_delivery)
- **Descrição**: Pagamento em dinheiro no momento da entrega
- **Funcionalidades**:
  - Controle de troco (opcional)
  - Validação do valor máximo de troco
  - Cálculo automático do troco a ser devolvido
- **Ícone**: 💵

### 2. Cartão na Entrega (card_delivery)
- **Descrição**: Pagamento com cartão de crédito no momento da entrega
- **Funcionalidades**:
  - Entregador leva máquina de cartão
  - Instruções específicas para o cliente
- **Ícone**: 💳

### 3. PIX na Entrega (pix_delivery)
- **Descrição**: Pagamento via PIX no momento da entrega
- **Funcionalidades**:
  - Entregador leva terminal PIX
  - Pagamento instantâneo
- **Ícone**: 📱

### 4. Débito na Entrega (debit_delivery)
- **Descrição**: Pagamento com cartão de débito no momento da entrega
- **Funcionalidades**:
  - Entregador leva máquina de débito
  - Instruções específicas para o cliente
- **Ícone**: 💳

## Estrutura do Banco de Dados

### Tabela `orders` - Novos Campos
```sql
- payment_method: VARCHAR(50) - Método de pagamento escolhido
- change_required: BOOLEAN - Se é necessário troco
- change_amount: DECIMAL(8,2) - Valor para o qual o cliente precisa de troco
- payment_notes: TEXT - Observações sobre o pagamento
```

### Tabela `shop_delivery_payment_settings`
```sql
- shop_id: BIGINT - ID da loja
- accept_cash_delivery: BOOLEAN - Aceita dinheiro na entrega
- accept_card_delivery: BOOLEAN - Aceita cartão na entrega
- accept_pix_delivery: BOOLEAN - Aceita PIX na entrega
- accept_debit_delivery: BOOLEAN - Aceita débito na entrega
- max_change_amount: DECIMAL(8,2) - Valor máximo de troco
- delivery_payment_instructions: TEXT - Instruções específicas da loja
```

### Tabela `payment_systems` - Novos Registros
```sql
- cash_delivery: Dinheiro na Entrega
- card_delivery: Cartão na Entrega
- pix_delivery: PIX na Entrega
- debit_delivery: Débito na Entrega
```

## API Endpoints

### GET /api/v1/rest/shops/{id}/delivery-payment-methods
Retorna os métodos de pagamento de entrega disponíveis para uma loja.

**Resposta:**
```json
{
  "data": {
    "shop_id": 1,
    "delivery_payment_methods": [
      {
        "tag": "cash_delivery",
        "name": "Dinheiro na Entrega",
        "icon": "💵",
        "description": "Pagamento em dinheiro no momento da entrega",
        "supports_change": true
      }
    ],
    "max_change_amount": 200.00,
    "instructions": "Instruções específicas da loja"
  }
}
```

### POST /api/v1/rest/orders
Criação de pedidos com suporte aos novos métodos de pagamento.

**Campos adicionais:**
```json
{
  "payment_method": "cash_delivery",
  "change_required": true,
  "change_amount": 100.00,
  "payment_notes": "Observações sobre o pagamento"
}
```

## Validações Implementadas

### 1. Validação de Método de Pagamento
- Verifica se o método de pagamento é aceito pela loja
- Valida se o método existe nos métodos disponíveis

### 2. Validação de Troco
- Para pagamento em dinheiro:
  - Se `change_required` é true, `change_amount` é obrigatório
  - `change_amount` deve ser maior que o valor total do pedido
  - `change_amount` não pode exceder o limite máximo da loja

### 3. Validação de Campos Condicionais
- `change_amount` só é válido para `cash_delivery`
- `change_required` só é válido para `cash_delivery`

## Aplicações Atualizadas

### 1. Flutter Customer App
- **Arquivos principais**:
  - `lib/presentation/pages/checkout/payment_method_selection.dart`
  - `lib/presentation/pages/checkout/delivery_payment_methods.dart`
  - `lib/presentation/component/delivery_payment_method_item.dart`
- **Funcionalidades**:
  - Seleção de método de pagamento de entrega
  - Interface para configurar troco
  - Validação em tempo real

### 2. Driver App
- **Arquivos principais**:
  - `lib/presentation/component/payment_info_card.dart`
  - `lib/infrastructure/models/data/order_detail.dart`
- **Funcionalidades**:
  - Exibição detalhada das informações de pagamento
  - Alertas para troco necessário
  - Instruções específicas por método

### 3. Seller App
- **Arquivos principais**:
  - `lib/presentation/component/list_items/order_item.dart`
  - `lib/infrastructure/models/data/order_data.dart`
- **Funcionalidades**:
  - Visualização do método de pagamento na lista
  - Indicação de troco necessário
  - Badges informativos

### 4. Admin Panel
- **Arquivos principais**:
  - `src/views/order/order-details.js`
  - `src/views/order/order-list.js`
- **Funcionalidades**:
  - Filtros por método de pagamento
  - Exibição detalhada das informações de pagamento
  - Gestão de configurações de loja

### 5. React Web App
- **Arquivos principais**:
  - `components/orderInfo/orderInfo.tsx`
  - `components/orderInfo/orderInfo.module.scss`
- **Funcionalidades**:
  - Exibição das informações de pagamento
  - Estilos específicos para cada método
  - Indicadores visuais para troco

## Testes Implementados

### 1. Testes de Feature
- `DeliveryPaymentMethodsTest.php`: Testa endpoint de métodos de pagamento
- `OrderWithBrazilianPaymentTest.php`: Testa criação de pedidos com novos métodos

### 2. Testes Unitários
- `OrderModelTest.php`: Testa funcionalidades do modelo Order
- `ShopDeliveryPaymentSettingTest.php`: Testa modelo de configurações

### 3. Cobertura de Testes
- Validações de entrada
- Regras de negócio
- Relacionamentos entre modelos
- Scopes e métodos auxiliares

## Configuração e Deploy

### 1. Migrações
Execute as migrações na seguinte ordem:
```bash
php artisan migrate --path=database/migrations/2024_01_15_000001_add_brazilian_payment_fields_to_orders_table.php
php artisan migrate --path=database/migrations/2024_01_15_000002_insert_brazilian_payment_methods.php
php artisan migrate --path=database/migrations/2024_01_15_000003_create_shop_delivery_payment_settings_table.php
php artisan migrate --path=database/migrations/2024_01_15_000004_enhance_transactions_table.php
```

### 2. Seeders
Execute os seeders para dados iniciais:
```bash
php artisan db:seed --class=BrazilianPaymentMethodsSeeder
```

### 3. Testes
Execute os testes para validar a implementação:
```bash
php artisan test --filter=DeliveryPaymentMethods
php artisan test --filter=OrderWithBrazilianPayment
php artisan test --filter=OrderModel
php artisan test --filter=ShopDeliveryPaymentSetting
```

## Considerações de Segurança

1. **Validação de Entrada**: Todos os campos são validados no backend
2. **Autorização**: Apenas usuários autenticados podem criar pedidos
3. **Sanitização**: Campos de texto são sanitizados para prevenir XSS
4. **Limites**: Valores de troco têm limites máximos configuráveis

## Monitoramento e Logs

1. **Logs de Pedidos**: Todos os pedidos com novos métodos são logados
2. **Métricas**: Acompanhamento de uso por método de pagamento
3. **Alertas**: Notificações para valores de troco altos
4. **Auditoria**: Rastreamento de mudanças nas configurações

## Próximos Passos

1. **Integração com Gateways**: Conectar com processadores de pagamento brasileiros
2. **Analytics**: Implementar dashboards de uso dos métodos
3. **Otimizações**: Melhorar performance das consultas
4. **Internacionalização**: Expandir para outros países
