<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EnhanceTransactionsForDeliveryPayments extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('delivery_payment_method', [
                'cash_delivery', 'card_delivery', 'pix_delivery', 'debit_delivery'
            ])->nullable()->after('status');

            $table->decimal('change_given', 10, 2)->nullable()->after('delivery_payment_method');
            $table->timestamp('payment_confirmed_at')->nullable()->after('change_given');
            $table->text('driver_notes')->nullable()->after('payment_confirmed_at');

            $table->index('delivery_payment_method');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropIndex(['delivery_payment_method']);
            $table->dropColumn([
                'delivery_payment_method', 'change_given',
                'payment_confirmed_at', 'driver_notes'
            ]);
        });
    }
}
