import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:foodyman/infrastructure/models/data/delivery_payment_method.dart';

part 'delivery_payment_state.freezed.dart';

@freezed
class DeliveryPaymentState with _$DeliveryPaymentState {
  const factory DeliveryPaymentState({
    @Default(false) bool isLoading,
    @Default([]) List<DeliveryPaymentMethod> methods,
    @Default('') String instructions,
    @Default('') String error,
    @Default(-1) int selectedIndex,
    @Default(false) bool changeRequired,
    @Default(0.0) double changeAmount,
  }) = _DeliveryPaymentState;

  const DeliveryPaymentState._();
}
