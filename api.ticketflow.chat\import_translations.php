<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

try {
    echo "Importando traduções em português...\n";
    
    $sql = file_get_contents('translations_dump_limpo_20250715_210910.sql');
    
    if (!$sql) {
        echo "Erro: Não foi possível ler o arquivo de dump.\n";
        exit(1);
    }
    
    DB::unprepared($sql);
    
    echo "Dump executado com sucesso!\n";
    
    // Verificar quantas traduções em português foram importadas
    $count = DB::table('translations')->where('locale', 'pt')->count();
    echo "Traduções em português importadas: {$count}\n";
    
    // Configurar idioma pt-BR na tabela languages
    echo "Configurando idioma pt-BR...\n";
    
    // Primeiro, remover o default do inglês
    DB::table('languages')->where('locale', 'en')->update(['default' => 0]);
    
    // Adicionar ou atualizar pt-BR como padrão
    DB::table('languages')->updateOrInsert(
        ['locale' => 'pt'],
        [
            'title' => 'Português (Brasil)',
            'default' => 1,
            'active' => 1,
            'backward' => 0,
            'deleted_at' => null,
            'created_at' => now(),
            'updated_at' => now()
        ]
    );
    
    echo "Idioma pt-BR configurado como padrão!\n";
    
    // Mostrar idiomas disponíveis
    $languages = DB::table('languages')->select('locale', 'title', 'default', 'active')->get();
    echo "Idiomas disponíveis:\n";
    foreach ($languages as $lang) {
        $default = $lang->default ? ' (PADRÃO)' : '';
        $active = $lang->active ? ' (ATIVO)' : ' (INATIVO)';
        echo "- {$lang->locale}: {$lang->title}{$default}{$active}\n";
    }
    
    echo "\nImportação concluída com sucesso!\n";
    
} catch (Exception $e) {
    echo "Erro durante a importação: " . $e->getMessage() . "\n";
    exit(1);
}
