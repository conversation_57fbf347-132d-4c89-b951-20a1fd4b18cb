import 'package:freezed_annotation/freezed_annotation.dart';

part 'delivery_payment_method.freezed.dart';
part 'delivery_payment_method.g.dart';

@freezed
class DeliveryPaymentMethod with _$DeliveryPaymentMethod {
  const factory DeliveryPaymentMethod({
    String? tag,
    String? name,
    String? icon,
    String? description,
    @Default(false) bool supportsChange,
    @Default(0.0) double maxChangeAmount,
  }) = _DeliveryPaymentMethod;

  factory DeliveryPaymentMethod.fromJson(Map<String, dynamic> json) =>
      _$DeliveryPaymentMethodFromJson(json);

  const DeliveryPaymentMethod._();
}
