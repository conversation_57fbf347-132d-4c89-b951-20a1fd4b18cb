<?php

namespace Tests\Feature;

use App\Models\Shop;
use App\Models\User;
use App\Models\ShopDeliveryPaymentSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeliveryPaymentMethodsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_delivery_payment_methods_for_shop()
    {
        // Create a user and shop
        $user = User::factory()->create();
        $shop = Shop::factory()->create(['user_id' => $user->id]);

        // Create delivery payment settings
        ShopDeliveryPaymentSetting::create([
            'shop_id' => $shop->id,
            'accept_cash_delivery' => true,
            'accept_card_delivery' => true,
            'accept_pix_delivery' => true,
            'accept_debit_delivery' => false,
            'max_change_amount' => 150.00,
            'delivery_payment_instructions' => 'Test instructions',
        ]);

        // Make request without middleware
        $response = $this->withoutMiddleware()->getJson("/api/v1/rest/shops/{$shop->id}/delivery-payment-methods");

        // Debug response
        if ($response->status() !== 200) {
            dump($response->json());
            dump($response->status());
        }

        // Assert response
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'shop_id',
                'delivery_payment_methods' => [
                    '*' => [
                        'tag',
                        'name',
                        'icon',
                        'description',
                        'supports_change',
                    ]
                ],
                'instructions',
            ]
        ]);

        $data = $response->json('data');
        $this->assertEquals($shop->id, $data['shop_id']);
        $this->assertEquals('Test instructions', $data['instructions']);
        $this->assertCount(3, $data['delivery_payment_methods']); // Only enabled methods
    }

    /** @test */
    public function it_returns_default_settings_when_no_settings_exist()
    {
        $user = User::factory()->create();
        $shop = Shop::factory()->create(['user_id' => $user->id]);

        $response = $this->withoutMiddleware()->getJson("/api/v1/rest/shops/{$shop->id}/delivery-payment-methods");

        $response->assertStatus(200);
        $data = $response->json('data');
        $this->assertCount(4, $data['delivery_payment_methods']); // All default methods enabled
    }

    /** @test */
    public function it_returns_404_for_non_existent_shop()
    {
        $response = $this->withoutMiddleware()->getJson("/api/v1/rest/shops/999/delivery-payment-methods");
        $response->assertStatus(404);
    }
}
