9999999999O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:23:"App\Models\EmailSetting":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:14:"email_settings";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:1;s:9:"smtp_auth";i:1;s:10:"smtp_debug";i:0;s:4:"host";s:20:"ssl://smtp.gmail.com";s:4:"port";i:465;s:8:"password";s:8:"password";s:7:"from_to";s:15:"<EMAIL>";s:9:"from_site";s:19:"foodyman.vercel.app";s:3:"ssl";s:85:"{"ssl": {"verify_peer": false, "verify_peer_name": false, "allow_self_signed": true}}";s:6:"active";i:0;s:10:"created_at";s:19:"2025-07-23 19:41:14";s:10:"updated_at";s:19:"2025-07-23 19:41:14";s:10:"deleted_at";N;}s:11:" * original";a:13:{s:2:"id";i:1;s:9:"smtp_auth";i:1;s:10:"smtp_debug";i:0;s:4:"host";s:20:"ssl://smtp.gmail.com";s:4:"port";i:465;s:8:"password";s:8:"password";s:7:"from_to";s:15:"<EMAIL>";s:9:"from_site";s:19:"foodyman.vercel.app";s:3:"ssl";s:85:"{"ssl": {"verify_peer": false, "verify_peer_name": false, "allow_self_signed": true}}";s:6:"active";i:0;s:10:"created_at";s:19:"2025-07-23 19:41:14";s:10:"updated_at";s:19:"2025-07-23 19:41:14";s:10:"deleted_at";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:3:"ssl";s:5:"array";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:2:"id";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}