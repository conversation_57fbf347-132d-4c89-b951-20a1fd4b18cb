<?php

namespace Tests\Feature;

use App\Models\Order;
use App\Models\Shop;
use App\Models\User;
use App\Models\ShopDeliveryPaymentSetting;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OrderWithBrazilianPaymentTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $shop;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->shop = Shop::factory()->create();
        
        // Create delivery payment settings
        ShopDeliveryPaymentSetting::create([
            'shop_id' => $this->shop->id,
            'accept_cash_delivery' => true,
            'accept_card_delivery' => true,
            'accept_pix_delivery' => true,
            'accept_debit_delivery' => true,
            'max_change_amount' => 200.00,
        ]);
    }

    /** @test */
    public function it_can_create_order_with_cash_delivery_payment()
    {
        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'cash_delivery',
            'change_required' => true,
            'change_amount' => 50.00,
            'payment_notes' => 'Please bring change for R$ 100',
            'total_price' => 50.00,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(201);
        
        $order = Order::latest()->first();
        $this->assertEquals('cash_delivery', $order->payment_method);
        $this->assertTrue($order->change_required);
        $this->assertEquals(50.00, $order->change_amount);
        $this->assertEquals('Please bring change for R$ 100', $order->payment_notes);
    }

    /** @test */
    public function it_can_create_order_with_card_delivery_payment()
    {
        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'card_delivery',
            'change_required' => false,
            'payment_notes' => 'Credit card payment on delivery',
            'total_price' => 75.50,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(201);
        
        $order = Order::latest()->first();
        $this->assertEquals('card_delivery', $order->payment_method);
        $this->assertFalse($order->change_required);
        $this->assertNull($order->change_amount);
    }

    /** @test */
    public function it_can_create_order_with_pix_delivery_payment()
    {
        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'pix_delivery',
            'change_required' => false,
            'total_price' => 120.00,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(201);
        
        $order = Order::latest()->first();
        $this->assertEquals('pix_delivery', $order->payment_method);
        $this->assertFalse($order->change_required);
    }

    /** @test */
    public function it_validates_change_amount_does_not_exceed_max_limit()
    {
        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'cash_delivery',
            'change_required' => true,
            'change_amount' => 250.00, // Exceeds max_change_amount of 200
            'total_price' => 50.00,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['change_amount']);
    }

    /** @test */
    public function it_validates_change_amount_is_greater_than_total_price()
    {
        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'cash_delivery',
            'change_required' => true,
            'change_amount' => 30.00, // Less than total_price
            'total_price' => 50.00,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['change_amount']);
    }

    /** @test */
    public function it_validates_payment_method_is_accepted_by_shop()
    {
        // Disable card delivery for this shop
        $this->shop->deliveryPaymentSetting()->update([
            'accept_card_delivery' => false,
        ]);

        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'card_delivery',
            'total_price' => 50.00,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['payment_method']);
    }

    /** @test */
    public function it_requires_change_amount_when_change_is_required()
    {
        $orderData = [
            'shop_id' => $this->shop->id,
            'user_id' => $this->user->id,
            'payment_method' => 'cash_delivery',
            'change_required' => true,
            // Missing change_amount
            'total_price' => 50.00,
            'delivery_type' => 'delivery',
        ];

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/rest/orders', $orderData);

        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['change_amount']);
    }
}
