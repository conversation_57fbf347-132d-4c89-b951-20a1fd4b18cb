<?php

namespace App\Http\Controllers\API\v1\Rest;

use App\Helpers\ResponseError;
use App\Helpers\Utility;
use App\Http\Requests\FilterParamsRequest;
use App\Http\Requests\Order\AddReviewRequest;
use App\Http\Requests\Shop\CheckWorkingDayRequest;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\ProductResource;
use App\Http\Resources\ReviewResource;
use App\Http\Resources\ShopGalleryResource;
use App\Http\Resources\ShopPaymentResource;
use App\Http\Resources\ShopResource;
use App\Jobs\UserActivityJob;
use App\Models\Order;
use App\Models\Settings;
use App\Models\Shop;
use App\Models\ShopDeliveryPaymentSetting;
use App\Models\ShopGallery;
use App\Repositories\Interfaces\ShopRepoInterface;
use App\Repositories\ReviewRepository\ReviewRepository;
use App\Repositories\ShopPaymentRepository\ShopPaymentRepository;
use App\Services\ShopServices\ShopReviewService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;
use Str;
use Throwable;

class ShopController extends RestBaseController
{
    /**
     * @param ShopRepoInterface $shopRepository
     */
    public function __construct(
        private ShopRepoInterface $shopRepository
    )
    {
        parent::__construct();
    }

    /**
     * Display a listing of the resource.
     *
     * @param FilterParamsRequest $request
     * @return AnonymousResourceCollection
     */
    public function paginate(FilterParamsRequest $request): AnonymousResourceCollection
    {
        $visibility = (int)Settings::where('key', 'by_subscription')->first()?->value;

        $merge = [
            'status'    => 'approved',
            'currency'  => $this->currency,
        ];

        if ($visibility) {
            $merge += ['visibility' => true];
        }

        $shops = $this->shopRepository->shopsPaginate(
            $request->merge($merge)->all()
        );

        return ShopResource::collection($shops);
    }

	/**
	 * Display a listing of the resource.
	 *
	 * @param FilterParamsRequest $request
	 * @return AnonymousResourceCollection
	 */
    public function selectPaginate(FilterParamsRequest $request): AnonymousResourceCollection
    {
        $shops = $this->shopRepository->selectPaginate(
            $request->merge([
                'status'        => 'approved',
                'currency'      => $this->currency
            ])->all()
        );

        return ShopResource::collection($shops);
    }

    /**
     * Display the specified resource.
     *
     * @param string $uuid
     * @return JsonResponse
     */
    public function show(string $uuid): JsonResponse
    {
        $shop = $this->shopRepository->shopDetails($uuid);

        if (!$shop) {
            return $this->onErrorResponse([
                'code'    => ResponseError::ERROR_404,
                'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        UserActivityJob::dispatchAfterResponse(
            $shop->id,
            get_class($shop),
            'click',
            1,
            auth('sanctum')->user()
        );

        return $this->successResponse(
            __('errors.' . ResponseError::SUCCESS, locale: $this->language),
            ShopResource::make($shop)
        );
    }

    /**
     * Display the specified resource.
     *
     * @param string $slug
     * @return JsonResponse
     */
    public function showSlug(string $slug): JsonResponse
    {
        $shop = $this->shopRepository->shopDetailsBySlug($slug);

        if (empty($shop)) {
            return $this->onErrorResponse(['code' => ResponseError::ERROR_404]);
        }

        /** @var Shop $shop */
        UserActivityJob::dispatchAfterResponse(
            $shop->id,
            get_class($shop),
            'click',
            1,
            auth('sanctum')->user()
        );

        return $this->successResponse(
            __('errors.' . ResponseError::NO_ERROR, locale: $this->language),
            ShopResource::make($shop)
        );
    }

    /**
     * Display the specified resource.
     *
     * @param FilterParamsRequest $request
     * @return JsonResponse
     */
    public function takes(FilterParamsRequest $request): JsonResponse
    {
        $shop = $this->shopRepository->takes($request->all());

        return $this->successResponse(
            __('errors.' . ResponseError::SUCCESS, locale: $this->language),
            $shop
        );
    }

    /**
     * Display the specified resource.
     *
     * @return JsonResponse
     */
    public function productsAvgPrices(): JsonResponse
    {
        $shop = $this->shopRepository->productsAvgPrices();

        return $this->successResponse(__('errors.' . ResponseError::SUCCESS, locale: $this->language), $shop);
    }

	/**
	 * Search shop Model from database.
	 *
	 * @param FilterParamsRequest $request
	 * @return AnonymousResourceCollection
	 */
    public function shopsSearch(FilterParamsRequest $request): AnonymousResourceCollection
    {
        $shops = $this->shopRepository->shopsSearch($request->merge([
            'status'        => 'approved',
            'currency'      => $this->currency
        ])->all());

        return ShopResource::collection($shops);
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param FilterParamsRequest $request
	 * @return AnonymousResourceCollection
	 */
    public function shopsByIDs(FilterParamsRequest $request): AnonymousResourceCollection
    {
        $shops = $this->shopRepository->shopsByIDs($request->merge(['status' => 'approved'])->all());

        return ShopResource::collection($shops);
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param FilterParamsRequest $request
	 *
	 * @return JsonResponse
	 */
	public function productsRecPaginate(FilterParamsRequest $request): JsonResponse
	{
		return $this->successResponse(__('web.products_found'),
			$this->shopRepository->productsRecPaginate($request->all())
		);
	}

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param FilterParamsRequest $request
	 * @return JsonResponse
	 */
    public function recommended(FilterParamsRequest $request): JsonResponse
    {
        return $this->successResponse(
            __('errors.' . ResponseError::SUCCESS, locale: $this->language),
            $this->shopRepository->recommended($request->all())
        );
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param FilterParamsRequest $request
	 *
	 * @return AnonymousResourceCollection
	 */
	public function branchProducts(FilterParamsRequest $request): AnonymousResourceCollection
	{
		return $this->shopRepository->branchProducts($request->all());
	}

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param int $id
	 * @param FilterParamsRequest $request
	 * @return JsonResponse
	 */
    public function products(int $id, FilterParamsRequest $request): JsonResponse
    {
        $shop = Shop::find($id);

        if (empty($shop)) {
            return $this->onErrorResponse([
                'code'    => ResponseError::ERROR_404,
                'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        return $this->successResponse(__('errors.' . ResponseError::SUCCESS, locale: $this->language),
            $this->shopRepository->products($request->merge(['shop_id' => $shop->id])->all())
        );
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param int $id
	 * @param FilterParamsRequest $request
	 * @return JsonResponse|AnonymousResourceCollection
	 */
    public function categories(int $id, FilterParamsRequest $request): JsonResponse|AnonymousResourceCollection
    {
        $shop = Shop::find($id);

        if (empty($shop)) {
            return $this->onErrorResponse([
                'code'    => ResponseError::ERROR_404,
                'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        $categories = $this->shopRepository->categories($request->merge(['shop_id' => $shop->id])->all());

        return CategoryResource::collection($categories);
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param int $id
	 * @param FilterParamsRequest $request
	 * @return JsonResponse|AnonymousResourceCollection
	 */
    public function productsPaginate(int $id, FilterParamsRequest $request): JsonResponse|AnonymousResourceCollection
    {
        $shop = Shop::find($id);

        if (empty($shop)) {
            return $this->onErrorResponse([
                'code'      => ResponseError::ERROR_404,
                'message'   => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        $products = $this->shopRepository->productsPaginate($request->merge(['shop_id' => $shop->id])->all());

        return ProductResource::collection($products);
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param int $id
	 * @param FilterParamsRequest $request
	 * @return JsonResponse|AnonymousResourceCollection
	 */
    public function productsRecommendedPaginate(int $id, FilterParamsRequest $request): JsonResponse|AnonymousResourceCollection
    {
        $shop = Shop::find($id);

        if (empty($shop)) {
            return $this->onErrorResponse([
                'code'    => ResponseError::ERROR_404,
                'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        $products = $this->shopRepository->productsRecommendedPaginate(
            $request->merge(['shop_id' => $shop->id])->all()
        );

        return ProductResource::collection($products);
    }

	/**
	 * Search shop Model from database via IDs.
	 *
	 * @param int $id
	 * @param FilterParamsRequest $request
	 * @return JsonResponse|AnonymousResourceCollection
	 */
    public function shopPayments(int $id, FilterParamsRequest $request): JsonResponse|AnonymousResourceCollection
    {
        $shop = Shop::find($id);

        if (empty($shop)) {
            return $this->onErrorResponse([
                'code'    => ResponseError::ERROR_404,
                'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        $payments = (new ShopPaymentRepository)->list($request->merge(['shop_id' => $shop->id])->all());

        return ShopPaymentResource::collection($payments);
    }

    /**
     * @param int $id
     * @return ShopGalleryResource|JsonResponse
     */
    public function galleries(int $id): ShopGalleryResource|JsonResponse
    {
		/** @var ShopGallery|null $shopGallery */
		$shopGallery = ShopGallery::with(['galleries'])
            ->where('shop_id', $id)
            ->where('active', 1)
            ->first();

        if (empty($shopGallery) || !$shopGallery->active) {
            return $this->onErrorResponse([
                'code'    => ResponseError::ERROR_404,
                'message' => __('errors.' . ResponseError::ERROR_404, locale: $this->language)
            ]);
        }

        return ShopGalleryResource::make($shopGallery);
    }

    /**
     * @param int $id
     * @param FilterParamsRequest $request
     * @return AnonymousResourceCollection
     */
    public function reviews(int $id, FilterParamsRequest $request): AnonymousResourceCollection
    {
        $filter = $request->merge([
            'type'      => 'order',
            'assign'    => 'shop',
            'assign_id' => $id,
        ])->all();

        $result = (new ReviewRepository)->paginate($filter, [
            'user' => fn($q) => $q
                ->select([
                    'id',
                    'uuid',
                    'firstname',
                    'lastname',
                    'password',
                    'img',
                    'active',
                ])
                ->withAvg('reviews', 'rating')
                ->withCount('reviews'),
            'reviewable:id,address',
        ]);

        return ReviewResource::collection($result);
    }

    /**
     * Add Review to Order.
     *
     * @param int $id
     * @param AddReviewRequest $request
     * @return JsonResponse
     */
    public function addReviews(int $id, AddReviewRequest $request): JsonResponse
    {
        $shop   = Shop::find($id);

        $result = (new ShopReviewService)->addReview($shop, $request->validated());

        if (!data_get($result, 'status')) {
            return $this->onErrorResponse($result);
        }

        return $this->successResponse(ResponseError::NO_ERROR, ShopResource::make(data_get($result, 'data')));

    }

    /**
     * @param int $id
     * @return float[]
     */
    public function reviewsGroupByRating(int $id): array
    {
        $reviews = DB::table('reviews')
            ->where('reviewable_type', Order::class)
            ->where('assignable_type', Shop::class)
            ->where('assignable_id', $id)
            ->whereNull('deleted_at')
            ->select([
                DB::raw('count(id) as count, avg(rating) as rating, rating')
            ])
            ->groupBy(['rating'])
            ->get();

        return [
            'group' => Utility::groupRating($reviews),
            'count' => $reviews->sum('count'),
            'avg'   => $reviews->avg('rating'),
        ];
    }

    /**
     * Search shop Model from database via IDs.
     *
     * @param int $id
     * @param CheckWorkingDayRequest $request
     * @return JsonResponse|AnonymousResourceCollection
     */
    public function shopWorkingCheck(int $id, CheckWorkingDayRequest $request): JsonResponse|AnonymousResourceCollection
    {
        $date = date('Y-m-d', strtotime($request->input('date')));
        $day  = Str::lower(date('l', strtotime($request->input('date'))));
        $time = date('H:i', strtotime($request->input('date')));

        $exists = false;

        $shop = Shop::whereHas('workingDays', fn($q) => $q
            ->where('disabled', 0)
            ->where('day', $day)
//            ->where('from', '>=', str_replace(':', '-', $time))
//            ->where('to', '<=', str_replace(':', '-', $time))
        )
            ->whereDoesntHave('closedDates', fn($q) => $q->where('date', $date))
            ->find($id);

        if (!empty($shop)) {

            foreach ($shop->workingDays as $workingDay) {

                try {
                    if (
                        $time >= date('H:i', strtotime(str_replace('-', ':', $workingDay->from))) &&
                        $time <= date('H:i', strtotime(str_replace('-', ':', $workingDay->to)))
                    ) {
                        $exists = true;
                    }
                } catch (Throwable) {

                }

            }

        }

        return $this->successResponse(__('errors.' . ResponseError::SUCCESS, locale: $this->language), $exists);
    }

    /**
     * Get delivery payment methods for a shop
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deliveryPaymentMethods(int $id): JsonResponse
    {
        try {
            $shop = Shop::with(['deliveryPaymentSettings'])->findOrFail($id);

            $settings = $shop->deliveryPaymentSettings ?? new ShopDeliveryPaymentSetting([
                'accept_cash_delivery' => true,
                'accept_card_delivery' => true,
                'accept_pix_delivery' => true,
                'accept_debit_delivery' => true,
                'max_change_amount' => 100.00,
            ]);

            $availableMethods = [];

            if ($settings->accept_cash_delivery) {
                $availableMethods[] = [
                    'tag' => 'cash_delivery',
                    'name' => 'Dinheiro (Físico)',
                    'icon' => '💵',
                    'description' => 'Pagamento em dinheiro na entrega',
                    'supports_change' => true,
                    'max_change_amount' => $settings->max_change_amount,
                ];
            }

            if ($settings->accept_card_delivery) {
                $availableMethods[] = [
                    'tag' => 'card_delivery',
                    'name' => 'Cartão na Máquina',
                    'icon' => '💳',
                    'description' => 'Cartão de crédito via máquina do entregador',
                    'supports_change' => false,
                ];
            }

            if ($settings->accept_pix_delivery) {
                $availableMethods[] = [
                    'tag' => 'pix_delivery',
                    'name' => 'PIX na Máquina',
                    'icon' => '📱',
                    'description' => 'PIX via terminal do entregador',
                    'supports_change' => false,
                ];
            }

            if ($settings->accept_debit_delivery) {
                $availableMethods[] = [
                    'tag' => 'debit_delivery',
                    'name' => 'Cartão de Débito',
                    'icon' => '💳',
                    'description' => 'Cartão de débito via máquina do entregador',
                    'supports_change' => false,
                ];
            }

            return $this->successResponse(__('errors.' . ResponseError::SUCCESS, locale: $this->language), [
                'shop_id' => $id,
                'delivery_payment_methods' => $availableMethods,
                'instructions' => $settings->delivery_payment_instructions,
            ]);

        } catch (Throwable $e) {
            $this->error($e);
            return $this->onErrorResponse(['code' => ResponseError::ERROR_404]);
        }
    }
}
